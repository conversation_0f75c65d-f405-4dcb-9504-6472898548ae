// Package config 提供配置文件的加载和管理功能
// 支持YAML格式的配置文件，包含服务器配置和各种区块链节点的连接信息
package config

import (
	"os"

	"gopkg.in/yaml.v2"

	"github.com/ethereum/go-ethereum/log"
)

// Server 定义服务器相关配置
type Server struct {
	Port string `yaml:"port"` // 服务监听端口
}

// Node 定义区块链节点的连接配置
// 包含RPC连接信息和数据API配置
type Node struct {
	RpcUrl       string `yaml:"rpc_url"`        // RPC节点URL
	RpcUser      string `yaml:"rpc_user"`       // RPC认证用户名
	RpcPass      string `yaml:"rpc_pass"`       // RPC认证密码
	DataApiUrl   string `yaml:"data_api_url"`   // 数据API的URL
	DataApiKey   string `yaml:"data_api_key"`   // 数据API的访问密钥
	DataApiToken string `yaml:"data_api_token"` // 数据API的访问令牌
	TimeOut      uint64 `yaml:"time_out"`       // 请求超时时间（秒）
}

// WalletNode 定义所有支持的区块链节点配置
// 包含主流的区块链网络，如以太坊、比特币、Solana等
type WalletNode struct {
	Eth     Node `yaml:"eth"`     // 以太坊主网
	Arbi    Node `yaml:"arbi"`    // Arbitrum L2网络
	Op      Node `yaml:"op"`      // Optimism L2网络
	Zksync  Node `yaml:"zksync"`  // zkSync L2网络
	Bsc     Node `yaml:"bsc"`     // Binance Smart Chain
	Heco    Node `yaml:"heco"`    // Huobi ECO Chain
	Avax    Node `yaml:"avax"`    // Avalanche网络
	Polygon Node `yaml:"polygon"` // Polygon网络
	Tron    Node `yaml:"tron"`    // Tron网络
	Sol     Node `yaml:"solana"`  // Solana网络
	Cosmos  Node `yaml:"cosmos"`  // Cosmos网络
	Aptos   Node `yaml:"aptos"`   // Aptos网络
	Mantle  Node `yaml:"mantle"`  // Mantle网络
	Scroll  Node `yaml:"scroll"`  // Scroll网络
	Base    Node `yaml:"evmbase"` // Base网络
	Linea   Node `yaml:"linea"`   // Linea网络
	Sui     Node `yaml:"sui"`     // Sui网络
	Ton     Node `yaml:"ton"`     // TON网络
	Xlm     Node `yaml:"xlm"`     // Stellar网络
	Icp     Node `yaml:"icp"`     // Internet Computer
	Btt     Node `yaml:"btt"`     // BitTorrent网络
}

// Config 定义应用程序的完整配置结构
type Config struct {
	Server     Server     `yaml:"server"`      // 服务器配置
	WalletNode WalletNode `yaml:"wallet_node"` // 区块链节点配置
	NetWork    string     `yaml:"network"`     // 网络环境（mainnet/testnet）
	Chains     []string   `yaml:"chains"`      // 启用的区块链列表
}

// New 从指定路径加载配置文件并创建Config实例
// 参数:
//   - path: 配置文件的路径
// 返回:
//   - *Config: 解析后的配置对象
//   - error: 如果加载或解析失败则返回错误
func New(path string) (*Config, error) {
	var config = new(Config)

	// 初始化日志处理器
	h := log.NewTerminalHandler(os.Stdout, true)
	log.SetDefault(log.NewLogger(h))

	// 读取配置文件内容
	data, err := os.ReadFile(path)
	if err != nil {
		log.Error("read config file error", "err", err)
		return nil, err
	}

	// 解析YAML格式的配置文件
	err = yaml.Unmarshal(data, config)
	if err != nil {
		log.Error("unmarshal config file error", "err", err)
		return nil, err
	}
	return config, nil
}

// 常量定义
const UnsupportedChain = "Unsupport chain"           // 不支持的区块链错误信息
const UnsupportedOperation = UnsupportedChain       // 不支持的操作错误信息
