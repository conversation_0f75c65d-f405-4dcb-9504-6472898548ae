name: <PERSON>et<PERSON>hainAccount Image Build Only
on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: environment to choice
        options:
          - testnet
          - mainnet
env:
  AWS_REGION : "ap-southeast-1"
  app: wallet-chain-account
permissions:
  id-token: write
  contents: read
run-name: Build Image For Repo
jobs:
  build:
    runs-on: self-hosted
    outputs:
      commit_sha: ${{ steps.get-commit-sha.outputs.commit_sha }}
      ecr_registry: ${{ steps.login-ecr.outputs.registry }}
    steps:
      - name: Checkout repo
        uses: actions/checkout@v3
      - name: Get Commit Sha
        id: get-commit-sha
        run: |
          echo "::set-output name=commit_sha::$(git rev-parse --short=7 HEAD)"
      - name: deploy app
        run: |
          registry="hkccr.ccs.tencentyun.com"
          namespace="dapplink"
          appname="wallet-chain-account"
          tag_name="${registry}/${namespace}/${appname}"
          docker login --username=************ -p dapplink@go hkccr.ccs.tencentyun.com
          docker build --pull  -t ${tag_name}:v0.0.3 .
          docker push ${tag_name}:v0.0.3
          
          HELM_OPTS="${HELM_OPTS} --set image.repository=${tag_name}"
          HELM_OPTS="${HELM_OPTS} --set image.tag=v0.0.3"
          if [ ${{github.event.inputs.environment }} == "testnet" ]; then
            helm template ${{ env.app }} -f values.yaml charts ${HELM_OPTS} 
            helm upgrade --install ${{ env.app }} -f values.yaml charts ${HELM_OPTS}
          else
            helm template ${{ env.app }} -f values.yaml charts -n mainnet ${HELM_OPTS} 
            helm upgrade --install ${{ env.app }} -f values.yaml charts -n mainnet ${HELM_OPTS}
          fi
            
