// Package main 是多链钱包账户服务的主入口点
// 该服务提供统一的gRPC接口来管理多种区块链的账户操作，包括：
// - 地址转换和验证
// - 区块查询
// - 交易发送和查询
// - 账户信息获取
// - 手续费估算等功能
package main

import (
	"flag"
	"net"

	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"github.com/ethereum/go-ethereum/log"

	"github.com/dapplink-labs/wallet-chain-account/chaindispatcher"
	"github.com/dapplink-labs/wallet-chain-account/config"
	wallet2 "github.com/dapplink-labs/wallet-chain-account/rpc/account"
)

// main 函数是应用程序的入口点
// 主要功能：
// 1. 解析命令行参数获取配置文件路径
// 2. 加载配置文件
// 3. 初始化链调度器
// 4. 创建并启动gRPC服务器
func main() {
	// 解析命令行参数，获取配置文件路径，默认为config.yml
	var f = flag.String("c", "config.yml", "config path")
	flag.Parse()

	// 加载配置文件
	conf, err := config.New(*f)
	if err != nil {
		panic(err)
	}

	// 初始化链调度器，用于管理多条区块链的适配器
	dispatcher, err := chaindispatcher.New(conf)
	if err != nil {
		log.Error("Setup dispatcher failed", "err", err)
		panic(err)
	}

	// 创建gRPC服务器，并注册拦截器用于统一处理请求日志和错误恢复
	grpcServer := grpc.NewServer(grpc.UnaryInterceptor(dispatcher.Interceptor))
	defer grpcServer.GracefulStop()

	// 注册钱包账户服务到gRPC服务器
	wallet2.RegisterWalletAccountServiceServer(grpcServer, dispatcher)

	// 监听TCP端口
	listen, err := net.Listen("tcp", ":"+conf.Server.Port)
	if err != nil {
		log.Error("net listen failed", "err", err)
		panic(err)
	}

	// 注册gRPC反射服务，便于调试和客户端发现服务
	reflection.Register(grpcServer)

	log.Info("dapplink wallet rpc services start success", "port", conf.Server.Port)

	// 启动gRPC服务器，开始监听和处理请求
	if err := grpcServer.Serve(listen); err != nil {
		log.Error("grpc server serve failed", "err", err)
		panic(err)
	}
}
