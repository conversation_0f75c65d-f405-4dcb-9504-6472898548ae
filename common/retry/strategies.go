package retry

import (
	"math"
	"math/rand"
	"time"
)

// Strategy 定义重试策略接口
// 不同的策略实现可以提供不同的重试间隔计算方法
type Strategy interface {
	// Duration 根据重试次数计算等待时间
	// 参数 attempt 是当前的重试次数（从0开始）
	Duration(attempt int) time.Duration
}

// ExponentialStrategy 指数退避重试策略
// 重试间隔按指数增长，可以避免对服务造成过大压力
type ExponentialStrategy struct {
	Min       time.Duration // 最小等待时间
	Max       time.Duration // 最大等待时间
	MaxJitter time.Duration // 最大抖动时间，用于避免惊群效应
}

// Duration 计算指数退避的等待时间
// 算法：基础时间 + 2^attempt * 1秒 + 随机抖动
// 参数:
//   - attempt: 重试次数（从0开始）
// 返回:
//   - time.Duration: 计算出的等待时间
func (e *ExponentialStrategy) Duration(attempt int) time.Duration {
	var jitter time.Duration // 非负抖动时间
	if e.MaxJitter > 0 {
		jitter = time.Duration(rand.Int63n(e.MaxJitter.Nanoseconds()))
	}

	// 如果重试次数为负数，返回最小时间加抖动
	if attempt < 0 {
		return e.Min + jitter
	}

	// 计算指数退避时间：Min + 2^attempt * 1秒
	durFloat := float64(e.Min)
	durFloat += math.Pow(2, float64(attempt)) * float64(time.Second)
	dur := time.Duration(durFloat)

	// 限制最大等待时间
	if durFloat > float64(e.Max) {
		dur = e.Max
	}

	// 添加随机抖动
	dur += jitter

	return dur
}

// Exponential 创建默认的指数退避策略
// 默认配置：最小时间0，最大时间10秒，最大抖动250毫秒
// 返回:
//   - Strategy: 配置好的指数退避策略实例
func Exponential() Strategy {
	return &ExponentialStrategy{
		Min:       0,                        // 最小等待时间为0
		Max:       10 * time.Second,         // 最大等待时间为10秒
		MaxJitter: 250 * time.Millisecond,   // 最大抖动为250毫秒
	}
}

// FixedStrategy 固定间隔重试策略
// 每次重试都使用相同的等待时间
type FixedStrategy struct {
	Dur time.Duration // 固定的等待时间
}

// Duration 返回固定的等待时间
// 无论重试次数如何，都返回相同的时间间隔
// 参数:
//   - attempt: 重试次数（此策略中忽略该参数）
// 返回:
//   - time.Duration: 固定的等待时间
func (f *FixedStrategy) Duration(attempt int) time.Duration {
	return f.Dur
}

// Fixed 创建固定间隔重试策略
// 参数:
//   - dur: 固定的重试间隔时间
// 返回:
//   - Strategy: 配置好的固定间隔策略实例
func Fixed(dur time.Duration) Strategy {
	return &FixedStrategy{
		Dur: dur,
	}
}
