// Package retry 提供重试机制的实现
// 支持多种重试策略，用于处理网络请求等可能失败的操作
package retry

import (
	"context"
	"fmt"
	"time"
)

// ErrFailedPermanently 表示操作在多次重试后仍然失败的错误
// 包含重试次数和最后一次的错误信息
type ErrFailedPermanently struct {
	attempts int   // 重试次数
	LastErr  error // 最后一次的错误
}

// Error 实现error接口，返回错误描述
func (e *ErrFailedPermanently) Error() string {
	return fmt.Sprintf("operation failed permanently after %d attempts: %v", e.attempts, e.LastErr)
}

// Unwrap 实现错误包装接口，返回原始错误
func (e *ErrFailedPermanently) Unwrap() error {
	return e.LastErr
}

// pair 是一个泛型结构体，用于包装两个返回值
type pair[T, U any] struct {
	a T
	b U
}

// Do2 执行返回两个值的操作，支持重试机制
// 这是Do函数的变体，用于处理返回两个值的函数
// 参数:
//   - ctx: 上下文，用于取消操作
//   - maxAttempts: 最大重试次数
//   - strategy: 重试策略，决定重试间隔
//   - op: 要执行的操作函数，返回两个值和一个错误
// 返回:
//   - T, U: 操作成功时的两个返回值
//   - error: 如果所有重试都失败则返回ErrFailedPermanently
func Do2[T, U any](ctx context.Context, maxAttempts int, strategy Strategy, op func() (T, U, error)) (T, U, error) {
	f := func() (pair[T, U], error) {
		a, b, err := op()
		return pair[T, U]{a, b}, err
	}
	res, err := Do(ctx, maxAttempts, strategy, f)
	return res.a, res.b, err
}

// Do 执行带重试机制的操作
// 根据指定的重试策略和最大重试次数执行操作，直到成功或达到最大重试次数
// 参数:
//   - ctx: 上下文，用于取消操作
//   - maxAttempts: 最大重试次数，必须大于0
//   - strategy: 重试策略，决定每次重试的间隔时间
//   - op: 要执行的操作函数
// 返回:
//   - T: 操作成功时的返回值
//   - error: 如果所有重试都失败则返回ErrFailedPermanently
func Do[T any](ctx context.Context, maxAttempts int, strategy Strategy, op func() (T, error)) (T, error) {
	var empty, ret T
	var err error

	// 验证重试次数参数
	if maxAttempts < 1 {
		return empty, fmt.Errorf("need at least 1 attempt to run op, but have %d max attempts", maxAttempts)
	}

	// 执行重试循环
	for i := 0; i < maxAttempts; i++ {
		// 检查上下文是否已取消
		if ctx.Err() != nil {
			return empty, ctx.Err()
		}

		// 执行操作
		ret, err = op()
		if err == nil {
			return ret, nil // 操作成功，立即返回
		}

		// 如果不是最后一次尝试，则等待重试间隔
		if i != maxAttempts-1 {
			time.Sleep(strategy.Duration(i))
		}
	}

	// 所有重试都失败，返回永久失败错误
	return empty, &ErrFailedPermanently{
		attempts: maxAttempts,
		LastErr:  err,
	}
}
