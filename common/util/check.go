// Package util 提供通用的工具函数
// 包含网络连接检查、数据验证等常用功能
package util

import (
	"net"
	"net/url"
	"time"
)

// IsURLAvailable 检查指定URL是否可访问
// 通过TCP连接测试来验证URL的可达性，主要用于检查RPC节点或API端点的可用性
// 参数:
//   - address: 要检查的URL地址
// 返回:
//   - bool: true表示URL可访问，false表示不可访问
func IsURLAvailable(address string) bool {
	// 解析URL获取主机和端口信息
	u, err := url.Parse(address)
	if err != nil {
		return false
	}

	addr := u.Host
	// 如果URL中没有指定端口，根据协议类型添加默认端口
	if u.Port() == "" {
		switch u.Scheme {
		case "http", "ws":
			addr += ":80"   // HTTP和WebSocket默认端口
		case "https", "wss":
			addr += ":443"  // HTTPS和安全WebSocket默认端口
		default:
			// 对于未知协议，假设可访问（开放策略）
			return true
		}
	}

	// 尝试建立TCP连接，超时时间为5秒
	conn, err := net.DialTimeout("tcp", addr, 5*time.Second)
	if err != nil {
		return false
	}

	// 立即关闭连接，我们只是测试连通性
	conn.Close()
	return true
}
