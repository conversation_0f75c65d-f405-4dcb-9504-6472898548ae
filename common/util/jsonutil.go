package util

import "encoding/json"

// ToJSON 将任意对象序列化为JSON字节数组
// 忽略序列化错误，适用于日志记录等场景
// 参数:
//   - v: 要序列化的对象
// 返回:
//   - []byte: JSON格式的字节数组，如果序列化失败返回nil
func ToJSON(v interface{}) []byte {
	b, _ := json.Marshal(v)
	return b
}

// ToJSONString 将任意对象序列化为JSON字符串
// 忽略序列化错误，适用于快速转换场景
// 参数:
//   - v: 要序列化的对象
// 返回:
//   - string: JSON格式的字符串，如果序列化失败返回空字符串
func ToJSONString(v interface{}) string {
	b, _ := json.Marshal(v)
	return string(b)
}

// ToPrettyJSON 将任意对象序列化为格式化的JSON字符串
// 使用缩进格式，便于阅读和调试
// 参数:
//   - v: 要序列化的对象
// 返回:
//   - string: 格式化的JSON字符串，如果序列化失败返回空字符串
func ToPrettyJSON(v interface{}) string {
	b, _ := json.MarshalIndent(v, "", "  ")
	return string(b)
}
