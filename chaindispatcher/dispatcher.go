// Package chaindispatcher 实现了多链调度器
// 该包负责管理多个区块链适配器，根据请求中的链名称将请求路由到对应的链适配器
// 提供统一的gRPC服务接口，支持多种区块链的操作
package chaindispatcher

import (
	"context"
	"runtime/debug"
	"strings"

	"github.com/dapplink-labs/wallet-chain-account/chain/arbitrum"
	"github.com/dapplink-labs/wallet-chain-account/chain/binance"
	"github.com/dapplink-labs/wallet-chain-account/chain/btt"
	"github.com/dapplink-labs/wallet-chain-account/chain/linea"
	"github.com/dapplink-labs/wallet-chain-account/chain/mantle"
	"github.com/dapplink-labs/wallet-chain-account/chain/optimism"
	"github.com/dapplink-labs/wallet-chain-account/chain/polygon"
	"github.com/dapplink-labs/wallet-chain-account/chain/scroll"
	"github.com/dapplink-labs/wallet-chain-account/chain/zksync"

	"github.com/ethereum/go-ethereum/log"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/dapplink-labs/wallet-chain-account/chain"
	"github.com/dapplink-labs/wallet-chain-account/chain/aptos"
	"github.com/dapplink-labs/wallet-chain-account/chain/cosmos"
	"github.com/dapplink-labs/wallet-chain-account/chain/ethereum"
	"github.com/dapplink-labs/wallet-chain-account/chain/solana"
	"github.com/dapplink-labs/wallet-chain-account/chain/sui"
	"github.com/dapplink-labs/wallet-chain-account/chain/ton"
	"github.com/dapplink-labs/wallet-chain-account/chain/tron"
	"github.com/dapplink-labs/wallet-chain-account/chain/xlm"
	"github.com/dapplink-labs/wallet-chain-account/config"
	"github.com/dapplink-labs/wallet-chain-account/rpc/account"
	"github.com/dapplink-labs/wallet-chain-account/rpc/common"
)

// CommonRequest 定义了通用请求接口
// 所有的请求都必须实现GetChain方法来指定目标区块链
type CommonRequest interface {
	GetChain() string
}

// CommonReply 定义了通用响应类型
type CommonReply = account.SupportChainsResponse

// ChainType 定义了区块链类型的别名
type ChainType = string

// ChainDispatcher 是多链调度器的核心结构
// 负责管理多个区块链适配器的注册表，并将请求路由到对应的适配器
type ChainDispatcher struct {
	registry map[ChainType]chain.IChainAdaptor // 区块链适配器注册表，key为链名称，value为对应的适配器实例
}

// New 创建并初始化一个新的链调度器实例
// 参数:
//   - conf: 应用程序配置，包含各个区块链的连接信息
// 返回:
//   - *ChainDispatcher: 初始化完成的调度器实例
//   - error: 如果初始化过程中出现错误
func New(conf *config.Config) (*ChainDispatcher, error) {
	dispatcher := ChainDispatcher{
		registry: make(map[ChainType]chain.IChainAdaptor),
	}

	// 定义区块链适配器工厂函数映射表
	// 每个区块链都有对应的工厂函数来创建适配器实例
	chainAdaptorFactoryMap := map[string]func(conf *config.Config) (chain.IChainAdaptor, error){
		strings.ToLower(ethereum.ChainName): ethereum.NewChainAdaptor, // 以太坊
		strings.ToLower(cosmos.ChainName):   cosmos.NewChainAdaptor,   // Cosmos
		strings.ToLower(solana.ChainName):   solana.NewChainAdaptor,   // Solana
		strings.ToLower(tron.ChainName):     tron.NewChainAdaptor,     // Tron
		strings.ToLower(aptos.ChainName):    aptos.NewChainAdaptor,    // Aptos
		strings.ToLower(sui.ChainName):      sui.NewSuiAdaptor,       // Sui
		strings.ToLower(ton.ChainName):      ton.NewChainAdaptor,      // TON
		strings.ToLower(polygon.ChainName):  polygon.NewChainAdaptor,  // Polygon
		strings.ToLower(zksync.ChainName):   zksync.NewChainAdaptor,   // zkSync
		strings.ToLower(arbitrum.ChainName): arbitrum.NewChainAdaptor, // Arbitrum
		strings.ToLower(binance.ChainName):  binance.NewChainAdaptor,  // Binance Smart Chain
		strings.ToLower(mantle.ChainName):   mantle.NewChainAdaptor,   // Mantle
		strings.ToLower(optimism.ChainName): optimism.NewChainAdaptor, // Optimism
		strings.ToLower(linea.ChainName):    linea.NewChainAdaptor,    // Linea
		strings.ToLower(scroll.ChainName):   scroll.NewChainAdaptor,   // Scroll
		strings.ToLower(xlm.ChainName):      xlm.NewChainAdaptor,      // Stellar
		strings.ToLower(btt.ChainName):      btt.NewChainAdaptor,      // BitTorrent
	}

	// 定义所有支持的区块链列表，用于错误提示
	supportedChains := []string{
		strings.ToLower(ethereum.ChainName), // 以太坊
		strings.ToLower(cosmos.ChainName),   // Cosmos
		strings.ToLower(solana.ChainName),   // Solana
		strings.ToLower(tron.ChainName),     // Tron
		strings.ToLower(sui.ChainName),      // Sui
		strings.ToLower(ton.ChainName),      // TON
		strings.ToLower(aptos.ChainName),    // Aptos
		strings.ToLower(polygon.ChainName),  // Polygon
		strings.ToLower(arbitrum.ChainName), // Arbitrum
		strings.ToLower(binance.ChainName),  // Binance Smart Chain
		strings.ToLower(mantle.ChainName),   // Mantle
		strings.ToLower(optimism.ChainName), // Optimism
		strings.ToLower(linea.ChainName),    // Linea
		strings.ToLower(scroll.ChainName),   // Scroll
		strings.ToLower(xlm.ChainName),      // Stellar
		strings.ToLower(btt.ChainName),      // BitTorrent
		strings.ToLower(zksync.ChainName),   // zkSync
	}

	// 根据配置文件中启用的链列表，初始化对应的适配器
	for _, c := range conf.Chains {
		chainName := strings.ToLower(c)
		if factory, ok := chainAdaptorFactoryMap[chainName]; ok {
			// 使用工厂函数创建适配器实例
			adaptor, err := factory(conf)
			if err != nil {
				log.Crit("failed to setup chain", "chain", chainName, "error", err)
			}
			// 将适配器注册到调度器的注册表中
			dispatcher.registry[chainName] = adaptor
		} else {
			// 如果配置的链不在支持列表中，记录错误日志
			log.Error("unsupported chain", "chain", chainName, "supportedChains", supportedChains)
		}
	}
	return &dispatcher, nil
}

// Interceptor 是gRPC的统一拦截器
// 功能包括：
// 1. 异常恢复：捕获panic并转换为gRPC错误
// 2. 请求日志：记录方法名、链名称和请求参数
// 3. 响应日志：记录处理结果
func (d *ChainDispatcher) Interceptor(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
	// 异常恢复机制，防止panic导致服务崩溃
	defer func() {
		if e := recover(); e != nil {
			log.Error("panic error", "msg", e)
			log.Debug(string(debug.Stack()))
			err = status.Errorf(codes.Internal, "Panic err: %v", e)
		}
	}()

	// 提取方法名用于日志记录
	pos := strings.LastIndex(info.FullMethod, "/")
	method := info.FullMethod[pos+1:]

	// 获取请求中的链名称并记录请求信息
	chainName := req.(CommonRequest).GetChain()
	log.Info(method, "chain", chainName, "req", req)

	// 调用实际的处理函数
	resp, err = handler(ctx, req)
	log.Debug("Finish handling", "resp", resp, "err", err)
	return
}

// preHandler 是请求的预处理函数
// 功能：
// 1. 提取并标准化链名称（转为小写）
// 2. 验证链是否在注册表中
// 3. 如果链不支持，返回错误响应
// 参数:
//   - req: 请求对象，必须实现CommonRequest接口
// 返回:
//   - resp: 如果链不支持则返回错误响应，否则返回nil
//   - chainName: 标准化后的链名称
func (d *ChainDispatcher) preHandler(req interface{}) (resp *CommonReply, chainName string) {
	chainName = strings.ToLower(req.(CommonRequest).GetChain())
	log.Debug("chain", chainName, "req", req)

	// 检查链是否在注册表中
	if _, ok := d.registry[chainName]; !ok {
		return &CommonReply{
			Code:    common.ReturnCode_ERROR,
			Msg:     config.UnsupportedOperation,
			Support: false,
		}, chainName
	}
	return nil, chainName
}

// GetSupportChains 获取指定链的支持信息
// 通过链调度器将请求路由到对应的链适配器
func (d *ChainDispatcher) GetSupportChains(ctx context.Context, request *account.SupportChainsRequest) (*account.SupportChainsResponse, error) {
	resp, chainName := d.preHandler(request)
	if resp != nil {
		return &account.SupportChainsResponse{
			Code: common.ReturnCode_ERROR,
			Msg:  config.UnsupportedOperation,
		}, nil
	}
	return d.registry[chainName].GetSupportChains(request)
}

// ConvertAddress 将公钥转换为指定链的地址格式
// 不同区块链有不同的地址生成算法，通过对应的适配器处理
func (d *ChainDispatcher) ConvertAddress(ctx context.Context, request *account.ConvertAddressRequest) (*account.ConvertAddressResponse, error) {
	resp, chainName := d.preHandler(request)
	if resp != nil {
		return &account.ConvertAddressResponse{
			Code: common.ReturnCode_ERROR,
			Msg:  "covert address fail at pre handle",
		}, nil
	}
	return d.registry[chainName].ConvertAddress(request)
}

// ValidAddress 验证地址格式是否符合指定链的规范
// 每个区块链都有特定的地址格式要求
func (d *ChainDispatcher) ValidAddress(ctx context.Context, request *account.ValidAddressRequest) (*account.ValidAddressResponse, error) {
	resp, chainName := d.preHandler(request)
	if resp != nil {
		return &account.ValidAddressResponse{
			Code: common.ReturnCode_ERROR,
			Msg:  "valid address error at pre handle",
		}, nil
	}
	return d.registry[chainName].ValidAddress(request)
}

func (d *ChainDispatcher) GetBlockByNumber(ctx context.Context, request *account.BlockNumberRequest) (*account.BlockResponse, error) {
	resp, chainName := d.preHandler(request)
	if resp != nil {
		return &account.BlockResponse{
			Code: common.ReturnCode_ERROR,
			Msg:  "get block by number fail at pre handle",
		}, nil
	}
	return d.registry[chainName].GetBlockByNumber(request)
}

func (d *ChainDispatcher) GetBlockByHash(ctx context.Context, request *account.BlockHashRequest) (*account.BlockResponse, error) {
	resp, chainName := d.preHandler(request)
	if resp != nil {
		return &account.BlockResponse{
			Code: common.ReturnCode_ERROR,
			Msg:  "get block by hash fail at pre handle",
		}, nil
	}
	return d.registry[chainName].GetBlockByHash(request)
}

func (d *ChainDispatcher) GetBlockHeaderByHash(ctx context.Context, request *account.BlockHeaderHashRequest) (*account.BlockHeaderResponse, error) {
	resp, chainName := d.preHandler(request)
	if resp != nil {
		return &account.BlockHeaderResponse{
			Code: common.ReturnCode_ERROR,
			Msg:  "get block header by hash fail at pre handle",
		}, nil
	}
	return d.registry[chainName].GetBlockHeaderByHash(request)
}

func (d *ChainDispatcher) GetBlockHeaderByNumber(ctx context.Context, request *account.BlockHeaderNumberRequest) (*account.BlockHeaderResponse, error) {
	resp, chainName := d.preHandler(request)
	if resp != nil {
		return &account.BlockHeaderResponse{
			Code: common.ReturnCode_ERROR,
			Msg:  "get block header by number fail at pre handle",
		}, nil
	}
	return d.registry[chainName].GetBlockHeaderByNumber(request)
}

func (d *ChainDispatcher) GetBlockHeaderByRange(ctx context.Context, request *account.BlockByRangeRequest) (*account.BlockByRangeResponse, error) {
	resp, chainName := d.preHandler(request)
	if resp != nil {
		return &account.BlockByRangeResponse{
			Code: common.ReturnCode_ERROR,
			Msg:  "get block range header fail at pre handle",
		}, nil
	}
	return d.registry[chainName].GetBlockByRange(request)
}

func (d *ChainDispatcher) GetAccount(ctx context.Context, request *account.AccountRequest) (*account.AccountResponse, error) {
	resp, chainName := d.preHandler(request)
	if resp != nil {
		return &account.AccountResponse{
			Code: common.ReturnCode_ERROR,
			Msg:  "get account information fail at pre handle",
		}, nil
	}
	return d.registry[chainName].GetAccount(request)
}

func (d *ChainDispatcher) GetFee(ctx context.Context, request *account.FeeRequest) (*account.FeeResponse, error) {
	resp, chainName := d.preHandler(request)
	if resp != nil {
		return &account.FeeResponse{
			Code: common.ReturnCode_ERROR,
			Msg:  "get fee fail at pre handle",
		}, nil
	}
	return d.registry[chainName].GetFee(request)
}

func (d *ChainDispatcher) SendTx(ctx context.Context, request *account.SendTxRequest) (*account.SendTxResponse, error) {
	resp, chainName := d.preHandler(request)
	if resp != nil {
		return &account.SendTxResponse{
			Code: common.ReturnCode_ERROR,
			Msg:  "send tx fail at pre handle",
		}, nil
	}
	return d.registry[chainName].SendTx(request)
}

func (d *ChainDispatcher) GetTxByAddress(ctx context.Context, request *account.TxAddressRequest) (*account.TxAddressResponse, error) {
	resp, chainName := d.preHandler(request)
	if resp != nil {
		return &account.TxAddressResponse{
			Code: common.ReturnCode_ERROR,
			Msg:  "get tx by address fail pre handle",
		}, nil
	}
	return d.registry[chainName].GetTxByAddress(request)
}

func (d *ChainDispatcher) GetTxByHash(ctx context.Context, request *account.TxHashRequest) (*account.TxHashResponse, error) {
	resp, chainName := d.preHandler(request)
	if resp != nil {
		return &account.TxHashResponse{
			Code: common.ReturnCode_ERROR,
			Msg:  "get tx by hash fail at pre handle",
		}, nil
	}
	return d.registry[chainName].GetTxByHash(request)
}

func (d *ChainDispatcher) GetBlockByRange(ctx context.Context, request *account.BlockByRangeRequest) (*account.BlockByRangeResponse, error) {
	resp, chainName := d.preHandler(request)
	if resp != nil {
		return &account.BlockByRangeResponse{
			Code: common.ReturnCode_ERROR,
			Msg:  "get blcok by range fail at pre handle",
		}, nil
	}
	return d.registry[chainName].GetBlockByRange(request)
}

func (d *ChainDispatcher) BuildUnSignTransaction(ctx context.Context, request *account.UnSignTransactionRequest) (*account.UnSignTransactionResponse, error) {
	resp, chainName := d.preHandler(request)
	if resp != nil {
		return &account.UnSignTransactionResponse{
			Code: common.ReturnCode_ERROR,
			Msg:  "get un sign tx fail at pre handle",
		}, nil
	}
	return d.registry[chainName].BuildUnSignTransaction(request)
}

func (d *ChainDispatcher) BuildSignedTransaction(ctx context.Context, request *account.SignedTransactionRequest) (*account.SignedTransactionResponse, error) {
	resp, chainName := d.preHandler(request)
	if resp != nil {
		return &account.SignedTransactionResponse{
			Code: common.ReturnCode_ERROR,
			Msg:  "signed tx fail at pre handle",
		}, nil
	}
	return d.registry[chainName].BuildSignedTransaction(request)
}

func (d *ChainDispatcher) DecodeTransaction(ctx context.Context, request *account.DecodeTransactionRequest) (*account.DecodeTransactionResponse, error) {
	resp, chainName := d.preHandler(request)
	if resp != nil {
		return &account.DecodeTransactionResponse{
			Code: common.ReturnCode_ERROR,
			Msg:  "decode tx fail at pre handle",
		}, nil
	}
	return d.registry[chainName].DecodeTransaction(request)
}

func (d *ChainDispatcher) VerifySignedTransaction(ctx context.Context, request *account.VerifyTransactionRequest) (*account.VerifyTransactionResponse, error) {
	resp, chainName := d.preHandler(request)
	if resp != nil {
		return &account.VerifyTransactionResponse{
			Code: common.ReturnCode_ERROR,
			Msg:  "verify tx fail at pre handle",
		}, nil
	}
	return d.registry[chainName].VerifySignedTransaction(request)
}

func (d *ChainDispatcher) GetExtraData(ctx context.Context, request *account.ExtraDataRequest) (*account.ExtraDataResponse, error) {
	resp, chainName := d.preHandler(request)
	if resp != nil {
		return &account.ExtraDataResponse{
			Code: common.ReturnCode_ERROR,
			Msg:  "get extra data fail at pre handle",
		}, nil
	}
	return d.registry[chainName].GetExtraData(request)
}

func (d *ChainDispatcher) GetNftListByAddress(ctx context.Context, request *account.NftAddressRequest) (*account.NftAddressResponse, error) {
	panic("implement me")
}

func (d *ChainDispatcher) GetNftCollection(ctx context.Context, request *account.NftCollectionRequest) (*account.NftCollectionResponse, error) {
	panic("implement me")
}

func (d *ChainDispatcher) GetNftDetail(ctx context.Context, request *account.NftDetailRequest) (*account.NftDetailResponse, error) {
	panic("implement me")
}

func (d *ChainDispatcher) GetNftHolderList(ctx context.Context, request *account.NftHolderListRequest) (*account.NftHolderListResponse, error) {
	panic("implement me")
}

func (d *ChainDispatcher) GetNftTradeHistory(ctx context.Context, request *account.NftTradeHistoryRequest) (*account.NftTradeHistoryResponse, error) {
	panic("implement me")
}

func (d *ChainDispatcher) GetAddressNftTradeHistory(ctx context.Context, request *account.AddressNftTradeHistoryRequest) (*account.AddressNftTradeHistoryResponse, error) {
	panic("implement me")
}
