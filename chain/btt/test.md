## 1.get free

- request
```
grpcurl -plaintext -d '{
  "chain": "Btt"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.getFee
```
- response
```
{
  "code": "SUCCESS",
  "msg": "get gas price success",
  "slow_fee": "****************|****************",
  "normal_fee": "****************|****************|*2",
  "fast_fee": "****************|****************|*3"
}

```


## 2.get support chain

- request
```
grpcurl -plaintext -d '{
  "chain": "Btt"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.getSupportChains
```
- response
```
{
  "code": "SUCCESS",
  "msg": "Support Chain",
  "support": true
}

```


## 3.get tx list by address

- request
```
grpcurl -plaintext -d '{
  "chain": "Btt",
  "address": "******************************************",
  "contractAddress": "******************************************"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.getTxByAddress
```
- response
```
{
  "code": "SUCCESS",
  "msg": "get tx list success",
  "tx": []
}

```

## 4.get tx by hash

- request
```
grpcurl -plaintext -d '{
  "chain": "Btt",
  "hash": "0xfe66799cd6de5b8a6a9657bf91cb64101d8c0f511b52ab644b43bb92688d2a26"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.getTxByHash
```
- response
```
{
  "code": "SUCCESS",
  "msg": "get tx by hash success",
  "tx": {
    "hash": "0xfe66799cd6de5b8a6a9657bf91cb64101d8c0f511b52ab644b43bb92688d2a26",
    "index": 6,
    "froms": [
      {
        "address": ""
      }
    ],
    "tos": [
      {
        "address": "******************************************"
      }
    ],
    "fee": "********",
    "status": "Success",
    "values": [
      {
        "value": "*********"
      }
    ],
    "type": 0,
    "height": "********",
    "contract_address": "******************************************",
    "datetime": "",
    "data": "A9059CBB0000000000000000000000005AC40EB0DCD19A64FEF09FA8A30E6AE2DD3F3AFB000000000000000000000000000000000000000000000000000000000632EA00"
  }
}

```

## 5.get account info 

- request
```
grpcurl -plaintext -d '{
  "chain": "Btt",
  "address": "******************************************",
  "contractAddress": "******************************************"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.getAccount
```
- response
```
{
  "code": "SUCCESS",
  "msg": "",
  "network": "",
  "account_number": "",
  "sequence": "2",
  "balance": "0"
}

```

