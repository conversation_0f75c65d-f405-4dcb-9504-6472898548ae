package evmbase

import (
	"encoding/hex"
	"math/big"

	"github.com/pkg/errors"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/rlp"
)

// BuildErc20Data 构建ERC-20代币转账的交易数据
// 生成调用ERC-20合约transfer方法的calldata
// 参数:
//   - toAddress: 接收方地址
//   - amount: 转账金额（以代币的最小单位计算）
// 返回:
//   - []byte: 编码后的交易数据
func BuildErc20Data(toAddress common.Address, amount *big.Int) []byte {
	var data []byte

	// ERC-20 transfer方法的函数签名
	transferFnSignature := []byte("transfer(address,uint256)")
	hash := crypto.Keccak256Hash(transferFnSignature)
	methodId := hash[:4] // 取前4字节作为方法ID

	// 将地址和金额编码为32字节的参数
	dataAddress := common.LeftPadBytes(toAddress.Bytes(), 32)
	dataAmount := common.LeftPadBytes(amount.Bytes(), 32)

	// 按照ABI编码规则组装数据：方法ID + 参数
	data = append(data, methodId...)
	data = append(data, dataAddress...)
	data = append(data, dataAmount...)

	return data
}

// BuildErc721Data 构建ERC-721 NFT转移的交易数据
// 生成调用ERC-721合约safeTransferFrom方法的calldata
// 参数:
//   - fromAddress: 发送方地址（当前NFT持有者）
//   - toAddress: 接收方地址
//   - tokenId: NFT的唯一标识符
// 返回:
//   - []byte: 编码后的交易数据
func BuildErc721Data(fromAddress, toAddress common.Address, tokenId *big.Int) []byte {
	var data []byte

	// ERC-721 safeTransferFrom方法的函数签名
	transferFnSignature := []byte("safeTransferFrom(address,address,uint256)")
	hash := crypto.Keccak256Hash(transferFnSignature)
	methodId := hash[:4] // 取前4字节作为方法ID

	// 将三个参数编码为32字节
	dataFromAddress := common.LeftPadBytes(fromAddress.Bytes(), 32)
	dataToAddress := common.LeftPadBytes(toAddress.Bytes(), 32)
	dataTokenId := common.LeftPadBytes(tokenId.Bytes(), 32)

	// 按照ABI编码规则组装数据：方法ID + 参数
	data = append(data, methodId...)
	data = append(data, dataFromAddress...)
	data = append(data, dataToAddress...)
	data = append(data, dataTokenId...)

	return data
}

// CreateLegacyUnSignTx 创建传统格式的未签名交易哈希
// 用于传统交易（非EIP-1559）的签名准备
// 参数:
//   - txData: 传统交易数据结构
//   - chainId: 链ID，用于防止跨链重放攻击
// 返回:
//   - string: 交易哈希的十六进制字符串
func CreateLegacyUnSignTx(txData *types.LegacyTx, chainId *big.Int) string {
	tx := types.NewTx(txData)
	signer := types.LatestSignerForChainID(chainId)
	txHash := signer.Hash(tx)
	return txHash.String()
}

// CreateEip1559UnSignTx 创建EIP-1559格式的未签名交易哈希
// 用于EIP-1559动态手续费交易的签名准备
// 参数:
//   - txData: EIP-1559交易数据结构
//   - chainId: 链ID，用于防止跨链重放攻击
// 返回:
//   - string: 交易哈希的十六进制字符串
//   - error: 如果创建失败则返回错误
func CreateEip1559UnSignTx(txData *types.DynamicFeeTx, chainId *big.Int) (string, error) {
	tx := types.NewTx(txData)
	// 获取对应链ID的签名器
	signer := types.LatestSignerForChainID(chainId)
	txHash := signer.Hash(tx)
	return txHash.String(), nil
}

// CreateLegacySignedTx 创建传统格式的已签名交易
// 将签名应用到传统交易上，生成可广播的交易数据
// 参数:
//   - txData: 传统交易数据结构
//   - signature: 交易签名字节数组
//   - chainId: 链ID
// 返回:
//   - string: 编码后的已签名交易数据（十六进制）
//   - string: 交易哈希
//   - error: 如果签名或编码失败则返回错误
func CreateLegacySignedTx(txData *types.LegacyTx, signature []byte, chainId *big.Int) (string, string, error) {
	tx := types.NewTx(txData)
	signer := types.LatestSignerForChainID(chainId)

	// 将签名应用到交易上
	signedTx, err := tx.WithSignature(signer, signature)
	if err != nil {
		return "", "", errors.New("tx with signature fail")
	}

	// 使用RLP编码将交易序列化为字节数组
	signedTxData, err := rlp.EncodeToBytes(signedTx)
	if err != nil {
		return "", "", errors.New("encode tx to byte fail")
	}

	return "0x" + hex.EncodeToString(signedTxData), signedTx.Hash().String(), nil
}

// CreateEip1559SignedTx 创建EIP-1559格式的已签名交易
// 将签名应用到EIP-1559交易上，生成可广播的交易数据
// 参数:
//   - txData: EIP-1559交易数据结构
//   - signature: 交易签名字节数组
//   - chainId: 链ID
// 返回:
//   - types.Signer: 签名器对象
//   - *types.Transaction: 已签名的交易对象
//   - string: 编码后的已签名交易数据（十六进制，去除类型前缀）
//   - string: 交易哈希
//   - error: 如果签名或编码失败则返回错误
func CreateEip1559SignedTx(txData *types.DynamicFeeTx, signature []byte, chainId *big.Int) (types.Signer, *types.Transaction, string, string, error) {
	tx := types.NewTx(txData)
	signer := types.LatestSignerForChainID(chainId)

	// 将签名应用到交易上
	signedTx, err := tx.WithSignature(signer, signature)
	if err != nil {
		return nil, nil, "", "", errors.New("tx with signature fail")
	}

	// 使用RLP编码将交易序列化为字节数组
	signedTxData, err := rlp.EncodeToBytes(signedTx)
	if err != nil {
		return nil, nil, "", "", errors.New("encode tx to byte fail")
	}

	// 注意：这里去除了前4个字节（交易类型前缀）
	return signer, signedTx, "0x" + hex.EncodeToString(signedTxData)[4:], signedTx.Hash().String(), nil
}
