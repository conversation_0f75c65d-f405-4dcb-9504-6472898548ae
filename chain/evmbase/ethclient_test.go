// Package evmbase 的测试文件
// 包含EVM基础功能的单元测试，用于验证以太坊客户端和数据查询功能
package evmbase

import (
	"context"
	"github.com/dapplink-labs/wallet-chain-account/config"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/log"
	"testing"
	"time"
)

// setup 测试环境初始化函数
// 加载配置文件并创建以太坊客户端和数据客户端实例
// 返回:
//   - ethClient: 以太坊RPC客户端
//   - ethData: 以太坊数据查询客户端
//   - err: 如果初始化失败则返回错误
func setup() (ethClient EthClient, ethData *EthData, err error) {
	// 加载配置文件
	conf, err := config.New("../../config.yml")
	if err != nil {
		log.Error("load config failed, error:", err)
		return nil, ethData, err
	}

	// 创建以太坊RPC客户端连接（这里使用Optimism节点进行测试）
	ethClient, err = DialEthClient(context.Background(), conf.WalletNode.Op.RpcUrl)
	if err != nil {
		return nil, ethData, err
	}

	// 创建以太坊数据API客户端（用于查询链上数据）
	ethDataClient, err := NewEthDataClient(conf.WalletNode.Op.DataApiUrl, conf.WalletNode.Polygon.DataApiKey, time.Duration(conf.WalletNode.Eth.TimeOut))
	if err != nil {
		return nil, ethData, err
	}

	return ethClient, ethDataClient, nil
}

// TestClnt_EthGetCode 测试获取地址代码功能
// 验证EthGetCode方法能够正确区分EOA和合约地址
func TestClnt_EthGetCode(t *testing.T) {
	// 初始化测试环境
	ethClient, _, err := setup()
	if err != nil {
		t.Error(err)
	}

	// 测试获取指定地址的代码类型
	// ****************************************** 是一个测试地址
	code, err := ethClient.EthGetCode(common.HexToAddress("******************************************"))
	if err != nil {
		t.Error(err)
	}

	// 输出结果：应该返回"eoa"或"contract"
	t.Log(code)
}
