// Package evmbase 提供EVM兼容区块链的通用基础功能
// 包含EIP-1559交易处理、以太坊客户端封装、ERC-20数据查询等核心功能
// 支持以太坊及其兼容链（如Polygon、Arbitrum、Optimism等）
package evmbase

// Eip1559DynamicFeeTx 定义EIP-1559动态手续费交易结构
// EIP-1559是以太坊伦敦硬分叉引入的手续费改进提案，主要特点：
// 1. 引入基础费用（BaseFee）机制，由网络自动调节
// 2. 用户设置最大费用（MaxFeePerGas）和优先费用（MaxPriorityFeePerGas）
// 3. 提供更好的手续费预测性和用户体验
type Eip1559DynamicFeeTx struct {
	ChainId     string `json:"chain_id"`     // 链ID，用于防止跨链重放攻击
	Nonce       uint64 `json:"nonce"`        // 交易序号，确保交易顺序和防止重放
	FromAddress string `json:"from_address"` // 发送方地址
	ToAddress   string `json:"to_address"`   // 接收方地址
	GasLimit    uint64 `json:"gas_limit"`    // Gas限制，交易最大可消耗的计算资源
	Gas         uint64 `json:"Gas"`          // 实际使用的Gas数量

	// EIP-1559动态手续费相关字段
	MaxFeePerGas         string `json:"max_fee_per_gas"`          // 每Gas最大费用（基础费用+优先费用的上限）
	MaxPriorityFeePerGas string `json:"max_priority_fee_per_gas"` // 每Gas最大优先费用（给矿工的小费上限）

	// 交易金额，支持多种代币类型
	Amount string `json:"amount"` // 转账金额（ETH或代币数量，以最小单位计算）

	// 智能合约地址，用于不同类型的代币交易
	// - 空值或零地址：ETH转账
	// - ERC-20合约地址：代币转账
	// - ERC-721合约地址：NFT转移
	// - ERC-1155合约地址：多代币标准
	ContractAddress string `json:"contract_address"`

	// 交易签名，可选字段（用于已签名交易）
	Signature string `json:"signature,omitempty"`
}
