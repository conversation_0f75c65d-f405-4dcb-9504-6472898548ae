package evmbase

import (
	"time"

	"github.com/ethereum/go-ethereum/log"

	"github.com/dapplink-labs/chain-explorer-api/common/account"
	"github.com/dapplink-labs/chain-explorer-api/common/chain"
	"github.com/dapplink-labs/chain-explorer-api/explorer/etherscan"
)

// EthData 封装以太坊数据查询客户端
// 提供与区块链浏览器API的交互功能，用于获取链上数据
type EthData struct {
	EthDataCli *etherscan.ChainExplorerAdaptor // 区块链浏览器适配器
}

// NewEthDataClient 创建新的以太坊数据客户端
// 初始化与区块链浏览器API的连接
// 参数:
//   - baseUrl: 区块链浏览器API的基础URL
//   - apiKey: API访问密钥
//   - timeout: 请求超时时间
// 返回:
//   - *EthData: 以太坊数据客户端实例
//   - error: 如果初始化失败则返回错误
func NewEthDataClient(baseUrl, apiKey string, timeout time.Duration) (*EthData, error) {
	// 创建区块链浏览器适配器实例
	etherscanCli, err := etherscan.NewChainExplorerAdaptor(apiKey, baseUrl, false, time.Duration(timeout))
	if err != nil {
		log.Error("New etherscan client fail", "err", err)
		return nil, err
	}
	return &EthData{EthDataCli: etherscanCli}, err
}

// GetTxByAddress 根据地址获取交易列表
// 通过区块链浏览器API查询指定地址的相关交易
// 参数:
//   - page: 页码（从1开始）
//   - pagesize: 每页数量
//   - address: 要查询的地址
//   - action: 交易类型（如"txlist"表示普通交易，"tokentx"表示代币交易）
// 返回:
//   - *account.TransactionResponse[account.AccountTxResponse]: 交易响应数据
//   - error: 如果查询失败则返回错误
func (ed *EthData) GetTxByAddress(page, pagesize uint64, address string, action account.ActionType) (*account.TransactionResponse[account.AccountTxResponse], error) {
	// 构造交易查询请求
	request := &account.AccountTxRequest{
		PageRequest: chain.PageRequest{
			Page:  page,     // 页码
			Limit: pagesize, // 每页数量
		},
		Action:  action,  // 交易类型
		Address: address, // 查询地址
	}

	// 调用区块链浏览器API获取交易数据
	txData, err := ed.EthDataCli.GetTxByAddress(request)
	if err != nil {
		return nil, err
	}
	return txData, nil
}

// GetBalanceByAddress 根据地址获取余额信息
// 支持ETH余额和ERC-20代币余额查询
// 参数:
//   - contractAddr: 合约地址（空字符串表示查询ETH余额）
//   - address: 要查询的地址
// 返回:
//   - *account.AccountBalanceResponse: 余额响应数据
//   - error: 如果查询失败则返回错误
func (ed *EthData) GetBalanceByAddress(contractAddr, address string) (*account.AccountBalanceResponse, error) {
	// 构造余额查询请求参数
	accountItem := []string{address}        // 查询地址列表
	symbol := []string{"ETH"}               // 代币符号
	contractAddress := []string{contractAddr} // 合约地址列表
	protocolType := []string{""}            // 协议类型
	page := []string{"1"}                   // 页码
	limit := []string{"10"}                 // 每页限制

	// 构造账户余额请求结构
	acbr := &account.AccountBalanceRequest{
		ChainShortName:  "ETH",           // 链的简称
		ExplorerName:    "etherescan",    // 浏览器名称
		Account:         accountItem,     // 账户地址
		Symbol:          symbol,          // 代币符号
		ContractAddress: contractAddress, // 合约地址
		ProtocolType:    protocolType,    // 协议类型
		Page:            page,            // 分页参数
		Limit:           limit,           // 限制参数
	}

	// 调用区块链浏览器API获取余额数据
	etherscanResp, err := ed.EthDataCli.GetAccountBalance(acbr)
	if err != nil {
		log.Error("get account balance error", "err", err)
		return nil, err
	}
	return etherscanResp, nil
}
