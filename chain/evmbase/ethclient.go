package evmbase

import (
	"context"
	"errors"
	"fmt"
	"math/big"
	"sync"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/log"
	"github.com/ethereum/go-ethereum/rpc"

	"github.com/dapplink-labs/wallet-chain-account/common/global_const"
	"github.com/dapplink-labs/wallet-chain-account/common/helpers"
	"github.com/dapplink-labs/wallet-chain-account/common/retry"
)

// 默认配置常量
const (
	defaultDialTimeout    = 5 * time.Second  // 默认连接超时时间
	defaultDialAttempts   = 5                // 默认连接重试次数
	defaultRequestTimeout = 10 * time.Second // 默认请求超时时间
)

// TransactionList 定义交易列表中的交易信息结构
// 用于RPC响应中的交易数据表示
type TransactionList struct {
	From  string `json:"from"`  // 交易发送方地址
	To    string `json:"to"`    // 交易接收方地址
	Hash  string `json:"hash"`  // 交易哈希
	Value string `json:"value"` // 交易金额
}

// RpcBlock 定义RPC调用返回的区块结构
// 包含区块基本信息和交易列表
type RpcBlock struct {
	Hash         common.Hash       `json:"hash"`         // 区块哈希
	Number       string            `json:"number"`       // 区块号（十六进制字符串）
	Transactions []TransactionList `json:"transactions"` // 交易列表
	BaseFee      string            `json:"baseFeePerGas"` // EIP-1559基础费用
}

// NumberUint64 将区块号从十六进制字符串转换为uint64
// 返回:
//   - uint64: 区块号的数值形式
//   - error: 如果转换失败则返回错误
func (b *RpcBlock) NumberUint64() (uint64, error) {
	return hexutil.DecodeUint64(b.Number)
}

// clnt 是以太坊客户端的内部实现结构
type clnt struct {
	rpc RPC // RPC客户端接口
}

// Logs 定义日志查询结果结构
// 包含日志列表和对应的区块头信息
type Logs struct {
	Logs          []types.Log   // 日志列表
	ToBlockHeader *types.Header // 查询结束区块的头信息
}

// EthClient 定义以太坊客户端接口
// 提供与以太坊节点交互的所有核心功能，包括区块查询、交易处理、状态查询等
// 该接口兼容所有EVM兼容的区块链网络
type EthClient interface {
	// 区块相关方法
	BlockHeaderByNumber(*big.Int) (*types.Header, error)                        // 根据区块号获取区块头
	BlockHeaderByHash(common.Hash) (*types.Header, error)                       // 根据区块哈希获取区块头
	BlockHeadersByRange(*big.Int, *big.Int, uint) ([]types.Header, error)       // 批量获取指定范围的区块头
	BlockByNumber(*big.Int) (*RpcBlock, error)                                  // 根据区块号获取完整区块信息
	BlockByHash(common.Hash) (*RpcBlock, error)                                 // 根据区块哈希获取完整区块信息
	LatestSafeBlockHeader() (*types.Header, error)                              // 获取最新安全区块头（PoS共识）
	LatestFinalizedBlockHeader() (*types.Header, error)                         // 获取最新最终确认区块头（PoS共识）

	// 交易相关方法
	TxCountByAddress(common.Address) (hexutil.Uint64, error)                    // 获取地址的交易计数（nonce）
	SuggestGasPrice() (*big.Int, error)                                         // 获取建议的Gas价格
	SuggestGasTipCap() (*big.Int, error)                                        // 获取建议的优先费用（EIP-1559）
	SendRawTransaction(rawTx string) (*common.Hash, error)                      // 发送原始交易数据
	TxByHash(common.Hash) (*types.Transaction, error)                           // 根据哈希获取交易信息
	TxReceiptByHash(common.Hash) (*types.Receipt, error)                        // 根据哈希获取交易收据

	// 状态查询方法
	StorageHash(common.Address, *big.Int) (common.Hash, error)                  // 获取合约存储哈希
	EthGetCode(common.Address) (string, error)                                  // 获取地址的代码（区分EOA和合约）
	GetBalance(address common.Address) (*big.Int, error)                        // 获取地址的ETH余额
	FilterLogs(filterQuery ethereum.FilterQuery, chainId uint) (Logs, error)    // 过滤查询事件日志

	// 连接管理
	Close() // 关闭客户端连接
}

// DialEthClient 创建以太坊客户端连接
// 使用指数退避重试机制确保连接的可靠性
// 参数:
//   - ctx: 上下文，用于控制连接超时
//   - rpcUrl: 以太坊节点的RPC URL
// 返回:
//   - EthClient: 以太坊客户端接口实例
//   - error: 如果连接失败则返回错误
func DialEthClient(ctx context.Context, rpcUrl string) (EthClient, error) {
	// 设置连接超时
	ctx, cancel := context.WithTimeout(ctx, defaultDialTimeout)
	defer cancel()

	// 使用指数退避策略进行重试连接
	bOff := retry.Exponential()
	rpcClient, err := retry.Do(ctx, defaultDialAttempts, bOff, func() (*rpc.Client, error) {
		// 首先检查URL的可用性
		if !helpers.IsURLAvailable(rpcUrl) {
			return nil, fmt.Errorf("address unavailable (%s)", rpcUrl)
		}

		// 尝试建立RPC连接
		client, err := rpc.DialContext(ctx, rpcUrl)
		if err != nil {
			return nil, fmt.Errorf("failed to dial address (%s): %w", rpcUrl, err)
		}

		return client, nil
	})

	if err != nil {
		return nil, err
	}

	// 返回封装后的客户端实例
	return &clnt{rpc: NewRPC(rpcClient)}, nil
}

// BlockHeaderByNumber 根据区块号获取区块头信息
// 参数:
//   - number: 区块号，nil表示获取最新区块
// 返回:
//   - *types.Header: 区块头信息
//   - error: 如果查询失败或区块不存在则返回错误
func (c *clnt) BlockHeaderByNumber(number *big.Int) (*types.Header, error) {
	ctxwt, cancel := context.WithTimeout(context.Background(), defaultRequestTimeout)
	defer cancel()

	var header *types.Header
	// 调用eth_getBlockByNumber RPC方法，false表示不包含完整交易信息
	err := c.rpc.CallContext(ctxwt, &header, "eth_getBlockByNumber", toBlockNumArg(number), false)
	if err != nil {
		log.Error("Call eth_getBlockByNumber method fail", "err", err)
		return nil, err
	} else if header == nil {
		log.Warn("header not found")
		return nil, ethereum.NotFound
	}

	return header, nil
}

// BlockHeaderByHash 根据区块哈希获取区块头信息
// 参数:
//   - hash: 区块哈希
// 返回:
//   - *types.Header: 区块头信息
//   - error: 如果查询失败或区块不存在则返回错误
func (c *clnt) BlockHeaderByHash(hash common.Hash) (*types.Header, error) {
	ctxwt, cancel := context.WithTimeout(context.Background(), defaultRequestTimeout)
	defer cancel()

	var header *types.Header
	// 调用eth_getBlockByHash RPC方法
	err := c.rpc.CallContext(ctxwt, &header, "eth_getBlockByHash", hash, false)
	if err != nil {
		return nil, err
	} else if header == nil {
		return nil, ethereum.NotFound
	}

	// 验证返回的区块头哈希是否与请求的哈希一致
	if header.Hash() != hash {
		return nil, errors.New("header mismatch")
	}

	return header, nil
}

// BlockHeadersByRange 批量获取指定范围内的区块头信息
// 使用批量RPC调用优化性能，对特定链（如ZkFair）使用并发处理
// 参数:
//   - startHeight: 起始区块高度
//   - endHeight: 结束区块高度
//   - chainId: 链ID，用于选择不同的处理策略
// 返回:
//   - []types.Header: 区块头列表
//   - error: 如果查询失败则返回错误
func (c *clnt) BlockHeadersByRange(startHeight, endHeight *big.Int, chainId uint) ([]types.Header, error) {
	// 如果起始和结束高度相同，直接返回单个区块头
	if startHeight.Cmp(endHeight) == 0 {
		header, err := c.BlockHeaderByNumber(startHeight)
		if err != nil {
			return nil, err
		}
		return []types.Header{*header}, nil
	}

	// 计算需要查询的区块数量
	count := new(big.Int).Sub(endHeight, startHeight).Uint64() + 1
	headers := make([]types.Header, count)
	batchElems := make([]rpc.BatchElem, count)
	ctxwt, cancel := context.WithTimeout(context.Background(), defaultRequestTimeout)
	defer cancel()

	// 对ZkFair链使用特殊的并发处理策略
	// 因为ZkFair链可能不支持标准的批量RPC调用
	if chainId == uint(global_const.ZkFairSepoliaChainId) ||
		chainId == uint(global_const.ZkFairChainId) {
		groupSize := 100 // 每组处理100个区块
		var wg sync.WaitGroup
		numGroups := (int(count)-1)/groupSize + 1
		wg.Add(numGroups)

		// 分组并发处理区块查询
		for i := 0; i < int(count); i += groupSize {
			start := i
			end := i + groupSize - 1
			if end > int(count) {
				end = int(count) - 1
			}
			go func(start, end int) {
				defer wg.Done()
				// 在每个goroutine中串行处理一组区块
				for j := start; j <= end; j++ {
					height := new(big.Int).Add(startHeight, new(big.Int).SetUint64(uint64(j)))
					batchElems[j] = rpc.BatchElem{
						Method: "eth_getBlockByNumber",
						Result: new(types.Header),
						Error:  nil,
					}
					header := new(types.Header)
					// 单独调用RPC方法而不是使用批量调用
					batchElems[j].Error = c.rpc.CallContext(ctxwt, header, batchElems[j].Method, toBlockNumArg(height), false)
					batchElems[j].Result = header
				}
			}(start, end)
		}

		wg.Wait()
	} else {
		// 对于标准EVM链，使用批量RPC调用提高效率
		for i := uint64(0); i < count; i++ {
			height := new(big.Int).Add(startHeight, new(big.Int).SetUint64(i))
			batchElems[i] = rpc.BatchElem{Method: "eth_getBlockByNumber", Args: []interface{}{toBlockNumArg(height), false}, Result: &headers[i]}
		}
		// 一次性发送所有RPC请求
		err := c.rpc.BatchCallContext(ctxwt, batchElems)
		if err != nil {
			return nil, err
		}
	}
	size := 0
	for i, batchElem := range batchElems {
		header, ok := batchElem.Result.(*types.Header)
		if !ok {
			return nil, fmt.Errorf("unable to transform rpc response %v into types.Header", batchElem.Result)
		}
		headers[i] = *header
		size = size + 1
	}
	headers = headers[:size]

	return headers, nil
}

func (c *clnt) BlockByNumber(number *big.Int) (*RpcBlock, error) {
	ctxwt, cancel := context.WithTimeout(context.Background(), defaultRequestTimeout)
	defer cancel()
	var block *RpcBlock
	err := c.rpc.CallContext(ctxwt, &block, "eth_getBlockByNumber", toBlockNumArg(number), true)
	if err != nil {
		log.Error("Call eth_getBlockByNumber method fail", "err", err)
		return nil, err
	} else if block == nil {
		log.Warn("header not found")
		return nil, ethereum.NotFound
	}
	return block, nil
}

func (c *clnt) BlockByHash(hash common.Hash) (*RpcBlock, error) {
	ctxwt, cancel := context.WithTimeout(context.Background(), defaultRequestTimeout)
	defer cancel()
	var block *RpcBlock
	err := c.rpc.CallContext(ctxwt, &block, "eth_getBlockByHash", hash, true)
	if err != nil {
		log.Error("Call eth_getBlockByHash method fail", "err", err)
		return nil, err
	} else if block == nil {
		log.Warn("header not found")
		return nil, ethereum.NotFound
	}
	return block, nil
}

func (c *clnt) LatestSafeBlockHeader() (*types.Header, error) {
	ctxwt, cancel := context.WithTimeout(context.Background(), defaultRequestTimeout)
	defer cancel()

	var header *types.Header
	err := c.rpc.CallContext(ctxwt, &header, "eth_getBlockByNumber", "safe", false)
	if err != nil {
		return nil, err
	} else if header == nil {
		return nil, ethereum.NotFound
	}

	return header, nil
}

func (c *clnt) LatestFinalizedBlockHeader() (*types.Header, error) {
	ctxwt, cancel := context.WithTimeout(context.Background(), defaultRequestTimeout)
	defer cancel()

	var header *types.Header
	err := c.rpc.CallContext(ctxwt, &header, "eth_getBlockByNumber", "finalized", false)
	if err != nil {
		return nil, err
	} else if header == nil {
		return nil, ethereum.NotFound
	}

	return header, nil
}

// TxCountByAddress 获取地址的交易计数（nonce值）
// nonce用于防止交易重放攻击，每发送一笔交易nonce递增1
// 参数:
//   - address: 要查询的地址
// 返回:
//   - hexutil.Uint64: 地址的当前nonce值
//   - error: 如果查询失败则返回错误
func (c *clnt) TxCountByAddress(address common.Address) (hexutil.Uint64, error) {
	ctxwt, cancel := context.WithTimeout(context.Background(), defaultRequestTimeout)
	defer cancel()
	var nonce hexutil.Uint64
	// 调用eth_getTransactionCount RPC方法，"latest"表示查询最新状态
	err := c.rpc.CallContext(ctxwt, &nonce, "eth_getTransactionCount", address, "latest")
	if err != nil {
		log.Error("Call eth_getTransactionCount method fail", "err", err)
		return 0, err
	}
	log.Info("get nonce by address success", "nonce", nonce)
	return nonce, err
}

// SuggestGasPrice 获取建议的Gas价格
// 用于传统交易（非EIP-1559）的手续费估算
// 返回:
//   - *big.Int: 建议的Gas价格（以wei为单位）
//   - error: 如果查询失败则返回错误
func (c *clnt) SuggestGasPrice() (*big.Int, error) {
	ctxwt, cancel := context.WithTimeout(context.Background(), defaultRequestTimeout)
	defer cancel()
	var hex hexutil.Big
	if err := c.rpc.CallContext(ctxwt, &hex, "eth_gasPrice"); err != nil {
		return nil, err
	}
	return (*big.Int)(&hex), nil
}

// SuggestGasTipCap 获取建议的优先费用
// 用于EIP-1559交易的优先费用（小费）估算
// 返回:
//   - *big.Int: 建议的优先费用（以wei为单位）
//   - error: 如果查询失败则返回错误
func (c *clnt) SuggestGasTipCap() (*big.Int, error) {
	ctxwt, cancel := context.WithTimeout(context.Background(), defaultRequestTimeout)
	defer cancel()
	var hex hexutil.Big
	if err := c.rpc.CallContext(ctxwt, &hex, "eth_maxPriorityFeePerGas"); err != nil {
		return nil, err
	}
	return (*big.Int)(&hex), nil
}

// SendRawTransaction 发送原始交易数据到网络
// 将已签名的交易广播到区块链网络
// 参数:
//   - rawTx: 十六进制编码的原始交易数据
// 返回:
//   - *common.Hash: 交易哈希
//   - error: 如果发送失败则返回错误
func (c *clnt) SendRawTransaction(rawTx string) (*common.Hash, error) {
	var txHash common.Hash
	ctxwt, cancel := context.WithTimeout(context.Background(), defaultRequestTimeout)
	defer cancel()
	if err := c.rpc.CallContext(ctxwt, &txHash, "eth_sendRawTransaction", rawTx); err != nil {
		return nil, err
	}
	log.Info("send tx to ethereum success", "txHash", txHash.Hex())
	return &txHash, nil
}

// TxByHash 根据交易哈希获取交易信息
// 查询指定哈希的交易详细信息
// 参数:
//   - hash: 交易哈希
// 返回:
//   - *types.Transaction: 交易对象
//   - error: 如果查询失败或交易不存在则返回错误
func (c *clnt) TxByHash(hash common.Hash) (*types.Transaction, error) {
	ctxwt, cancel := context.WithTimeout(context.Background(), defaultRequestTimeout)
	defer cancel()

	var tx *types.Transaction
	err := c.rpc.CallContext(ctxwt, &tx, "eth_getTransactionByHash", hash)
	if err != nil {
		return nil, err
	} else if tx == nil {
		return nil, ethereum.NotFound
	}

	return tx, nil
}

// TxReceiptByHash 根据交易哈希获取交易收据
// 交易收据包含交易执行结果、Gas使用情况、事件日志等信息
// 参数:
//   - hash: 交易哈希
// 返回:
//   - *types.Receipt: 交易收据对象
//   - error: 如果查询失败或收据不存在则返回错误
func (c *clnt) TxReceiptByHash(hash common.Hash) (*types.Receipt, error) {
	ctxwt, cancel := context.WithTimeout(context.Background(), defaultRequestTimeout)
	defer cancel()

	var txReceipt *types.Receipt
	err := c.rpc.CallContext(ctxwt, &txReceipt, "eth_getTransactionReceipt", hash)
	if err != nil {
		return nil, err
	} else if txReceipt == nil {
		return nil, ethereum.NotFound
	}

	return txReceipt, nil
}

// StorageHash 获取指定地址在特定区块的存储哈希
// 用于验证合约存储状态的完整性
// 参数:
//   - address: 合约地址
//   - blockNumber: 区块号
// 返回:
//   - common.Hash: 存储哈希值
//   - error: 如果查询失败则返回错误
func (c *clnt) StorageHash(address common.Address, blockNumber *big.Int) (common.Hash, error) {
	ctxwt, cancel := context.WithTimeout(context.Background(), defaultRequestTimeout)
	defer cancel()

	// 使用eth_getProof获取存储证明，从中提取存储哈希
	proof := struct{ StorageHash common.Hash }{}
	err := c.rpc.CallContext(ctxwt, &proof, "eth_getProof", address, nil, toBlockNumArg(blockNumber))
	if err != nil {
		return common.Hash{}, err
	}

	return proof.StorageHash, nil
}

// EthGetCode 获取地址的代码并判断地址类型
// 用于区分外部账户（EOA）和智能合约账户
// 参数:
//   - account: 要查询的地址
// 返回:
//   - string: "eoa"表示外部账户，"contract"表示合约账户
//   - error: 如果查询失败则返回错误
func (c *clnt) EthGetCode(account common.Address) (string, error) {
	ctxwt, cancel := context.WithTimeout(context.Background(), defaultRequestTimeout)
	defer cancel()

	var result hexutil.Bytes
	err := c.rpc.CallContext(ctxwt, &result, "eth_getCode", account, "latest")
	if err != nil {
		return "", err
	}

	// 如果代码为空（0x），则是外部账户；否则是合约账户
	if result.String() == "0x" {
		return "eoa", nil      // 外部拥有账户（Externally Owned Account）
	} else {
		return "contract", nil // 智能合约账户
	}
}

// GetBalance 获取地址的ETH余额
// 查询指定地址在最新区块的ETH余额
// 参数:
//   - address: 要查询的地址
// 返回:
//   - *big.Int: 余额（以wei为单位）
//   - error: 如果查询失败则返回错误
func (c *clnt) GetBalance(address common.Address) (*big.Int, error) {
	ctxwt, cancel := context.WithTimeout(context.Background(), defaultRequestTimeout)
	defer cancel()

	var result hexutil.Big
	err := c.rpc.CallContext(ctxwt, &result, "eth_getBalance", address, "latest")
	if err != nil {
		return nil, fmt.Errorf("get balance failed: %w", err)
	}

	balance := (*big.Int)(&result)
	return balance, nil
}

// FilterLogs 过滤查询事件日志
// 根据指定条件查询区块链上的事件日志，支持不同链的特殊处理
// 参数:
//   - query: 日志过滤查询条件（包含地址、主题、区块范围等）
//   - chainId: 链ID，用于选择不同的处理策略
// 返回:
//   - Logs: 包含日志列表和目标区块头的结构
//   - error: 如果查询失败则返回错误
func (c *clnt) FilterLogs(query ethereum.FilterQuery, chainId uint) (Logs, error) {
	// 将查询条件转换为RPC调用参数
	arg, err := toFilterArg(query)
	if err != nil {
		return Logs{}, err
	}

	var logs []types.Log
	var header types.Header

	// 准备批量RPC调用：同时获取区块头和日志
	batchElems := make([]rpc.BatchElem, 2)
	batchElems[0] = rpc.BatchElem{Method: "eth_getBlockByNumber", Args: []interface{}{toBlockNumArg(query.ToBlock), false}, Result: &header}
	batchElems[1] = rpc.BatchElem{Method: "eth_getLogs", Args: []interface{}{arg}, Result: &logs}

	// 日志查询可能耗时较长，使用更长的超时时间
	ctxwt, cancel := context.WithTimeout(context.Background(), defaultRequestTimeout*10)
	defer cancel()

	// 对ZkFair链使用特殊处理（不支持批量调用）
	if chainId == uint(global_const.ZkFairSepoliaChainId) ||
		chainId == uint(global_const.ZkFairChainId) {
		// 分别调用两个RPC方法
		batchElems[0].Error = c.rpc.CallContext(ctxwt, &header, batchElems[0].Method, toBlockNumArg(query.ToBlock), false)
		batchElems[1].Error = c.rpc.CallContext(ctxwt, &logs, batchElems[1].Method, arg)
	} else {
		// 对于标准EVM链，使用批量RPC调用提高效率
		err = c.rpc.BatchCallContext(ctxwt, batchElems)
		if err != nil {
			return Logs{}, err
		}
	}

	// 检查区块头查询结果
	if batchElems[0].Error != nil {
		return Logs{}, fmt.Errorf("unable to query for the `FilterQuery#ToBlock` header: %w", batchElems[0].Error)
	}

	// 检查日志查询结果
	if batchElems[1].Error != nil {
		return Logs{}, fmt.Errorf("unable to query logs: %w", batchElems[1].Error)
	}

	return Logs{Logs: logs, ToBlockHeader: &header}, nil
}

// Close 关闭以太坊客户端连接
// 释放底层RPC连接资源
func (c *clnt) Close() {
	c.rpc.Close()
}

// GetRPC 获取底层RPC客户端
// 用于需要直接访问RPC功能的场景
// 返回:
//   - RPC: RPC客户端接口
func (c *clnt) GetRPC() RPC {
	return c.rpc
}

// RPC 定义RPC客户端接口
// 封装了基本的RPC调用功能
type RPC interface {
	Close()                                                                   // 关闭连接
	CallContext(ctx context.Context, result any, method string, args ...any) error // 单个RPC调用
	BatchCallContext(ctx context.Context, b []rpc.BatchElem) error            // 批量RPC调用
}

// rpcClient RPC客户端的具体实现
type rpcClient struct {
	rpc *rpc.Client // go-ethereum的RPC客户端
}

// NewRPC 创建新的RPC客户端包装器
// 参数:
//   - client: go-ethereum的RPC客户端实例
// 返回:
//   - RPC: RPC接口实现
func NewRPC(client *rpc.Client) RPC {
	return &rpcClient{client}
}

// Close 关闭RPC连接
func (c *rpcClient) Close() {
	c.rpc.Close()
}

// CallContext 执行单个RPC调用
// 封装go-ethereum的RPC调用方法
// 参数:
//   - ctx: 上下文，用于控制超时和取消
//   - result: 用于接收结果的指针
//   - method: RPC方法名
//   - args: RPC方法参数
// 返回:
//   - error: 如果调用失败则返回错误
func (c *rpcClient) CallContext(ctx context.Context, result any, method string, args ...any) error {
	err := c.rpc.CallContext(ctx, result, method, args...)
	return err
}

// BatchCallContext 执行批量RPC调用
// 一次性发送多个RPC请求，提高网络效率
// 参数:
//   - ctx: 上下文，用于控制超时和取消
//   - b: 批量RPC调用元素列表
// 返回:
//   - error: 如果批量调用失败则返回错误
func (c *rpcClient) BatchCallContext(ctx context.Context, b []rpc.BatchElem) error {
	err := c.rpc.BatchCallContext(ctx, b)
	return err
}

// toBlockNumArg 将区块号转换为RPC调用参数
// 处理不同类型的区块号表示方式
// 参数:
//   - number: 区块号，nil表示最新区块
// 返回:
//   - string: 格式化后的区块号参数
func toBlockNumArg(number *big.Int) string {
	if number == nil {
		return "latest" // 最新区块
	}
	if number.Sign() >= 0 {
		return hexutil.EncodeBig(number) // 正数区块号转为十六进制
	}
	// 负数区块号（如-1表示最新区块）
	return rpc.BlockNumber(number.Int64()).String()
}

// toFilterArg 将日志过滤查询转换为RPC参数
// 构造eth_getLogs方法的参数格式
// 参数:
//   - q: 以太坊日志过滤查询条件
// 返回:
//   - interface{}: 格式化后的过滤参数
//   - error: 如果参数冲突则返回错误
func toFilterArg(q ethereum.FilterQuery) (interface{}, error) {
	// 基本过滤参数：地址和主题
	arg := map[string]interface{}{"address": q.Addresses, "topics": q.Topics}

	if q.BlockHash != nil {
		// 使用特定区块哈希进行过滤
		arg["blockHash"] = *q.BlockHash
		// 区块哈希和区块范围不能同时指定
		if q.FromBlock != nil || q.ToBlock != nil {
			return nil, errors.New("cannot specify both BlockHash and FromBlock/ToBlock")
		}
	} else {
		// 使用区块范围进行过滤
		if q.FromBlock == nil {
			arg["fromBlock"] = "0x0" // 从创世区块开始
		} else {
			arg["fromBlock"] = toBlockNumArg(q.FromBlock)
		}
		arg["toBlock"] = toBlockNumArg(q.ToBlock)
	}
	return arg, nil
}
