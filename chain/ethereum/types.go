package ethereum

// Eip1559DynamicFeeTx 定义EIP-1559动态手续费交易结构
// EIP-1559是以太坊的手续费改进提案，引入了基础费用和优先费用的概念
// 这种交易类型支持更精确的手续费控制和更好的用户体验
type Eip1559DynamicFeeTx struct {
	ChainId     string `json:"chain_id"`     // 链ID，用于防止重放攻击
	Nonce       uint64 `json:"nonce"`        // 交易序号，防止重复交易
	FromAddress string `json:"from_address"` // 发送方地址
	ToAddress   string `json:"to_address"`   // 接收方地址
	GasLimit    uint64 `json:"gas_limit"`    // Gas限制，交易最大可消耗的Gas数量
	Gas         uint64 `json:"gas"`          // 实际使用的Gas数量

	// EIP-1559动态手续费相关字段
	MaxFeePerGas         string `json:"max_fee_per_gas"`          // 每Gas最大费用（基础费用+优先费用）
	MaxPriorityFeePerGas string `json:"max_priority_fee_per_gas"` // 每Gas最大优先费用（给矿工的小费）

	// 交易金额和合约相关
	Amount string `json:"amount"` // 转账金额（ETH或ERC-20代币数量）

	// 智能合约地址，用于不同类型的代币交易
	// - ERC-20: 代币合约地址
	// - ERC-721: NFT合约地址
	// - ERC-1155: 多代币合约地址
	ContractAddress string `json:"contract_address"`
}
