// Package ethereum 实现了以太坊区块链的适配器
// 提供以太坊网络的账户管理、交易处理、区块查询等功能
// 支持ERC-20代币操作和智能合约交互
package ethereum

import (
	"context"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"math/big"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/log"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"github.com/status-im/keycard-go/hexutils"

	account2 "github.com/dapplink-labs/chain-explorer-api/common/account"
	"github.com/dapplink-labs/wallet-chain-account/chain"
	"github.com/dapplink-labs/wallet-chain-account/chain/evmbase"
	erc20_base2 "github.com/dapplink-labs/wallet-chain-account/chain/evmbase"
	"github.com/dapplink-labs/wallet-chain-account/common/util"
	"github.com/dapplink-labs/wallet-chain-account/config"
	"github.com/dapplink-labs/wallet-chain-account/rpc/account"
	common2 "github.com/dapplink-labs/wallet-chain-account/rpc/common"
)

// ChainName 定义以太坊链的名称常量
const ChainName = "Ethereum"

// ChainAdaptor 以太坊链适配器结构体
// 实现了chain.IChainAdaptor接口，提供以太坊网络的完整功能
type ChainAdaptor struct {
	ethClient     erc20_base2.EthClient // 以太坊RPC客户端，用于与节点通信
	ethDataClient *erc20_base2.EthData  // 以太坊数据API客户端，用于获取链上数据
}

// NewChainAdaptor 创建新的以太坊链适配器实例
// 初始化RPC客户端和数据API客户端
// 参数:
//   - conf: 应用程序配置，包含以太坊节点连接信息
//
// 返回:
//   - chain.IChainAdaptor: 实现了链适配器接口的以太坊适配器
//   - error: 如果初始化失败则返回错误
func NewChainAdaptor(conf *config.Config) (chain.IChainAdaptor, error) {
	// 连接以太坊RPC节点
	ethClient, err := erc20_base2.DialEthClient(context.Background(), conf.WalletNode.Eth.RpcUrl)
	if err != nil {
		return nil, err
	}

	// 初始化以太坊数据API客户端
	ethDataClient, err := erc20_base2.NewEthDataClient(conf.WalletNode.Eth.DataApiUrl, conf.WalletNode.Eth.DataApiKey, time.Second*15)
	if err != nil {
		return nil, err
	}

	return &ChainAdaptor{
		ethClient:     ethClient,
		ethDataClient: ethDataClient,
	}, nil
}

// GetSupportChains 获取以太坊链的支持信息
// 返回该适配器支持以太坊链的确认信息
func (c *ChainAdaptor) GetSupportChains(req *account.SupportChainsRequest) (*account.SupportChainsResponse, error) {
	return &account.SupportChainsResponse{
		Code:    common2.ReturnCode_SUCCESS,
		Msg:     "Support this chain",
		Support: true,
	}, nil
}

// ConvertAddress 将公钥转换为以太坊地址
// 使用以太坊的地址生成算法：Keccak256(公钥)[12:]
// 参数:
//   - req: 包含公钥的转换请求
//
// 返回:
//   - *account.ConvertAddressResponse: 包含生成的以太坊地址
//   - error: 总是返回nil（错误信息包含在响应中）
func (c *ChainAdaptor) ConvertAddress(req *account.ConvertAddressRequest) (*account.ConvertAddressResponse, error) {
	// 解码十六进制格式的公钥
	publicKeyBytes, err := hex.DecodeString(req.PublicKey)
	if err != nil {
		log.Error("decode public key failed:", err)
		return &account.ConvertAddressResponse{
			Code:    common2.ReturnCode_ERROR,
			Msg:     "convert address fail",
			Address: common.Address{}.String(),
		}, nil
	}

	// 使用以太坊地址生成算法：Keccak256哈希的后20字节
	// 注意：跳过公钥的第一个字节（压缩标识符）
	addressCommon := common.BytesToAddress(crypto.Keccak256(publicKeyBytes[1:])[12:])
	return &account.ConvertAddressResponse{
		Code:    common2.ReturnCode_SUCCESS,
		Msg:     "convert address success",
		Address: addressCommon.String(),
	}, nil
}
 
// ValidAddress 验证以太坊地址格式是否正确
// 以太坊地址格式：0x + 40位十六进制字符
// 参数:
//   - req: 包含待验证地址的请求
//
// 返回:
//   - *account.ValidAddressResponse: 包含验证结果
//   - error: 总是返回nil（错误信息包含在响应中）
func (c *ChainAdaptor) ValidAddress(req *account.ValidAddressRequest) (*account.ValidAddressResponse, error) {
	// 检查地址长度和前缀
	if len(req.Address) != 42 || !strings.HasPrefix(req.Address, "0x") {
		return &account.ValidAddressResponse{
			Code:  common2.ReturnCode_SUCCESS,
			Msg:   "invalid address",
			Valid: false,
		}, nil
	}

	// 验证地址是否为有效的十六进制字符串
	ok := regexp.MustCompile("^[0-9a-fA-F]{40}$").MatchString(req.Address[2:])
	if ok {
		return &account.ValidAddressResponse{
			Code:  common2.ReturnCode_SUCCESS,
			Msg:   "valid address",
			Valid: true,
		}, nil
	} else {
		return &account.ValidAddressResponse{
			Code:  common2.ReturnCode_SUCCESS,
			Msg:   "invalid address",
			Valid: false,
		}, nil
	}
}

// GetBlockHeaderByNumber 根据区块号获取区块头信息
// 支持获取最新区块（height=0）或指定高度的区块头
// 参数:
//   - req: 包含区块高度的请求，height=0表示获取最新区块
// 返回:
//   - *account.BlockHeaderResponse: 包含区块头详细信息
//   - error: 总是返回nil（错误信息包含在响应中）
func (c *ChainAdaptor) GetBlockHeaderByNumber(req *account.BlockHeaderNumberRequest) (*account.BlockHeaderResponse, error) {
	var blockNumber *big.Int
	if req.Height == 0 {
		blockNumber = nil // 获取最新区块
	} else {
		blockNumber = big.NewInt(req.Height) // 获取指定高度的区块
	}

	// 从以太坊节点获取区块头信息
	blockInfo, err := c.ethClient.BlockHeaderByNumber(blockNumber)
	if err != nil {
		log.Error("get latest block header fail", "err", err)
		return &account.BlockHeaderResponse{
			Code: common2.ReturnCode_ERROR,
			Msg:  "get latest block header fail",
		}, nil
	}

	log.Info("get block success", "blockInf", blockInfo, "Number", blockInfo.Number.String(), "Hash", blockInfo.Hash().Hex(), "ParentHash", blockInfo.ParentHash.String())

	// 构造标准化的区块头响应结构
	blockHead := &account.BlockHeader{
		Hash:             blockInfo.Hash().Hex(),                              // 区块哈希
		ParentHash:       blockInfo.ParentHash.String(),                       // 父区块哈希
		UncleHash:        blockInfo.UncleHash.String(),                        // 叔块哈希
		CoinBase:         blockInfo.Coinbase.String(),                         // 矿工地址
		Root:             blockInfo.Root.String(),                             // 状态树根哈希
		TxHash:           blockInfo.TxHash.String(),                           // 交易树根哈希
		ReceiptHash:      blockInfo.ReceiptHash.String(),                      // 收据树根哈希
		ParentBeaconRoot: common.Hash{}.String(),                              // 信标链父根（EIP-4788）
		Difficulty:       blockInfo.Difficulty.String(),                       // 挖矿难度
		Number:           blockInfo.Number.String(),                           // 区块号
		GasLimit:         blockInfo.GasLimit,                                  // Gas限制
		GasUsed:          blockInfo.GasUsed,                                   // 已使用Gas
		Time:             blockInfo.Time,                                      // 区块时间戳
		Extra:            hex.EncodeToString(blockInfo.Extra),                 // 额外数据
		MixDigest:        blockInfo.MixDigest.String(),                        // 混合哈希
		Nonce:            strconv.FormatUint(blockInfo.Nonce.Uint64(), 10),    // 随机数
		BaseFee:          blockInfo.BaseFee.String(),                          // EIP-1559基础费用
		WithdrawalsHash:  common.Hash{}.String(),                              // 提取哈希（EIP-4895）
		BlobGasUsed:      0,                                                   // Blob Gas使用量（EIP-4844）
		ExcessBlobGas:    0,                                                   // 过量Blob Gas（EIP-4844）
	}
	return &account.BlockHeaderResponse{
		Code:        common2.ReturnCode_SUCCESS,
		Msg:         "get latest block header success",
		BlockHeader: blockHead,
	}, nil
}

// GetBlockHeaderByHash 根据区块哈希获取区块头信息
// 通过区块哈希精确定位并获取特定区块的头部信息
// 参数:
//   - req: 包含区块哈希的请求
// 返回:
//   - *account.BlockHeaderResponse: 包含区块头详细信息
//   - error: 总是返回nil（错误信息包含在响应中）
func (c *ChainAdaptor) GetBlockHeaderByHash(req *account.BlockHeaderHashRequest) (*account.BlockHeaderResponse, error) {
	// 从以太坊节点根据哈希获取区块头
	blockInfo, err := c.ethClient.BlockHeaderByHash(common.HexToHash(req.Hash))
	if err != nil {
		log.Error("get latest block header fail", "err", err)
		return &account.BlockHeaderResponse{
			Code: common2.ReturnCode_ERROR,
			Msg:  "get latest block header fail",
		}, nil
	}

	// 构造完整的区块头信息，包含所有以太坊区块头字段
	blockHeader := &account.BlockHeader{
		Hash:             blockInfo.Hash().String(),                        // 区块哈希
		ParentHash:       blockInfo.ParentHash.String(),                    // 父区块哈希
		UncleHash:        blockInfo.UncleHash.String(),                     // 叔块哈希
		CoinBase:         blockInfo.Coinbase.String(),                      // 矿工地址（区块奖励接收者）
		Root:             blockInfo.Root.String(),                          // 状态树根哈希
		TxHash:           blockInfo.TxHash.String(),                        // 交易Merkle树根哈希
		ReceiptHash:      blockInfo.ReceiptHash.String(),                   // 收据Merkle树根哈希
		ParentBeaconRoot: blockInfo.ParentBeaconRoot.String(),              // 信标链父根（用于PoS共识）
		Difficulty:       blockInfo.Difficulty.String(),                    // 挖矿难度值
		Number:           blockInfo.Number.String(),                        // 区块编号
		GasLimit:         blockInfo.GasLimit,                               // 区块Gas限制
		GasUsed:          blockInfo.GasUsed,                                // 区块实际使用的Gas
		Time:             blockInfo.Time,                                   // 区块时间戳
		Extra:            string(blockInfo.Extra),                          // 矿工可自定义的额外数据
		MixDigest:        blockInfo.MixDigest.String(),                     // PoW混合哈希
		Nonce:            strconv.FormatUint(blockInfo.Nonce.Uint64(), 10), // PoW随机数
		BaseFee:          blockInfo.BaseFee.String(),                       // EIP-1559基础费用
		WithdrawalsHash:  blockInfo.WithdrawalsHash.String(),               // 提取操作哈希（上海升级）
		BlobGasUsed:      *blockInfo.BlobGasUsed,                           // Blob数据使用的Gas（Cancun升级）
		ExcessBlobGas:    *blockInfo.ExcessBlobGas,                         // 过量Blob Gas（Cancun升级）
	}
	return &account.BlockHeaderResponse{
		Code:        common2.ReturnCode_SUCCESS,
		Msg:         "get latest block header success",
		BlockHeader: blockHeader,
	}, nil
}

// GetBlockByNumber 根据区块号获取完整区块信息
// 包含区块基本信息和所有交易列表
// 参数:
//   - req: 包含区块高度的请求
// 返回:
//   - *account.BlockResponse: 包含区块信息和交易列表
//   - error: 总是返回nil（错误信息包含在响应中）
func (c *ChainAdaptor) GetBlockByNumber(req *account.BlockNumberRequest) (*account.BlockResponse, error) {
	// 从以太坊节点获取指定高度的完整区块信息
	block, err := c.ethClient.BlockByNumber(big.NewInt(req.Height))
	if err != nil {
		log.Error("block by number error", err)
		return &account.BlockResponse{
			Code: common2.ReturnCode_ERROR,
			Msg:  "block by number error",
		}, nil
	}

	blockNumber, _ := block.NumberUint64()

	// 处理区块中的所有交易，转换为标准格式
	var txListRet []*account.BlockInfoTransactionList
	for _, v := range block.Transactions {
		bitlItem := &account.BlockInfoTransactionList{
			From:           v.From,           // 交易发送方地址
			To:             v.To,             // 交易接收方地址
			TokenAddress:   v.To,             // 代币合约地址（对于ETH转账，等同于接收方）
			ContractWallet: v.To,             // 合约钱包地址
			Hash:           v.Hash,           // 交易哈希
			Height:         blockNumber,      // 区块高度
			Amount:         v.Value,          // 交易金额
		}
		txListRet = append(txListRet, bitlItem)
	}

	return &account.BlockResponse{
		Code:         common2.ReturnCode_SUCCESS,
		Msg:          "block by number success",
		Height:       int64(blockNumber),    // 区块高度
		Hash:         block.Hash.String(),   // 区块哈希
		BaseFee:      block.BaseFee,         // EIP-1559基础费用
		Transactions: txListRet,             // 交易列表
	}, nil
}

// GetBlockByHash 根据区块哈希获取完整区块信息
// 通过区块哈希精确获取特定区块的详细信息和交易列表
// 参数:
//   - req: 包含区块哈希的请求
// 返回:
//   - *account.BlockResponse: 包含区块信息和交易列表
//   - error: 总是返回nil（错误信息包含在响应中）
func (c *ChainAdaptor) GetBlockByHash(req *account.BlockHashRequest) (*account.BlockResponse, error) {
	// 根据哈希从以太坊节点获取完整区块信息
	block, err := c.ethClient.BlockByHash(common.HexToHash(req.Hash))
	if err != nil {
		log.Error("block by number error", err)
		return &account.BlockResponse{
			Code: common2.ReturnCode_ERROR,
			Msg:  "block by number error",
		}, nil
	}

	// 提取并格式化区块中的所有交易信息
	var txListRet []*account.BlockInfoTransactionList
	for _, v := range block.Transactions {
		bitlItem := &account.BlockInfoTransactionList{
			From:   v.From,   // 交易发送方
			To:     v.To,     // 交易接收方
			Hash:   v.Hash,   // 交易哈希
			Amount: v.Value,  // 交易金额
		}
		txListRet = append(txListRet, bitlItem)
	}

	blockNumber, _ := block.NumberUint64()
	return &account.BlockResponse{
		Code:         common2.ReturnCode_SUCCESS,
		Msg:          "block by hash success",
		Height:       int64(blockNumber),    // 区块高度
		Hash:         block.Hash.String(),   // 区块哈希
		BaseFee:      block.BaseFee,         // EIP-1559基础费用
		Transactions: txListRet,             // 交易列表
	}, nil
}

// GetAccount 获取以太坊账户信息
// 包括账户余额、nonce值等关键信息，支持ETH和ERC-20代币查询
// 参数:
//   - req: 包含地址和可选合约地址的请求
// 返回:
//   - *account.AccountResponse: 包含账户余额、序列号等信息
//   - error: 如果获取余额失败可能返回错误
func (c *ChainAdaptor) GetAccount(req *account.AccountRequest) (*account.AccountResponse, error) {
	// 获取账户的nonce值（交易序列号）
	// nonce用于防止重放攻击，每发送一笔交易nonce递增1
	nonceResult, err := c.ethClient.TxCountByAddress(common.HexToAddress(req.Address))
	if err != nil {
		log.Error("get nonce by address fail", "err", err)
		return &account.AccountResponse{
			Code: common2.ReturnCode_ERROR,
			Msg:  "get nonce by address fail",
		}, nil
	}

	// 获取账户余额
	// 如果ContractAddress为空，获取ETH余额
	// 如果ContractAddress不为空，获取对应ERC-20代币余额
	balanceResult, err := c.ethDataClient.GetBalanceByAddress(req.ContractAddress, req.Address)
	if err != nil {
		return &account.AccountResponse{
			Code:    common2.ReturnCode_ERROR,
			Msg:     "get token balance fail",
			Balance: "0",
		}, err
	}
	log.Info("balance result", "balance=", balanceResult.Balance, "balanceStr=", balanceResult.BalanceStr)

	// 处理余额数据，确保返回有效的字符串格式
	balanceStr := "0"
	if balanceResult.Balance != nil && balanceResult.Balance.Int() != nil {
		balanceStr = balanceResult.Balance.Int().String()
	}

	// 将nonce转换为字符串格式
	sequence := strconv.FormatUint(uint64(nonceResult), 10)

	return &account.AccountResponse{
		Code:          common2.ReturnCode_SUCCESS,
		Msg:           "get account response success",
		AccountNumber: "0",        // 以太坊不使用账户编号，固定为0
		Sequence:      sequence,   // 账户nonce值，用于交易排序
		Balance:       balanceStr, // 账户余额（wei单位）
	}, nil
}

// GetFee 获取以太坊网络的手续费估算
// 支持EIP-1559动态手续费机制，提供不同速度级别的费用选项
// 参数:
//   - req: 手续费请求参数
// 返回:
//   - *account.FeeResponse: 包含慢速、正常、快速三种费用选项
//   - error: 总是返回nil（错误信息包含在响应中）
func (c *ChainAdaptor) GetFee(req *account.FeeRequest) (*account.FeeResponse, error) {
	// 获取建议的Gas价格（用于传统交易）
	gasPrice, err := c.ethClient.SuggestGasPrice()
	if err != nil {
		log.Error("get gas price failed", "err", err)
		return &account.FeeResponse{
			Code: common2.ReturnCode_ERROR,
			Msg:  "get suggest gas price fail",
		}, nil
	}

	// 获取建议的优先费用（EIP-1559交易的小费）
	gasTipCap, err := c.ethClient.SuggestGasTipCap()
	if err != nil {
		log.Error("get gas price failed", "err", err)
		return &account.FeeResponse{
			Code: common2.ReturnCode_ERROR,
			Msg:  "get suggest gas price fail",
		}, nil
	}

	// 返回三种不同速度的手续费选项
	// 格式：gasPrice|gasTipCap[|倍数]
	return &account.FeeResponse{
		Code:      common2.ReturnCode_SUCCESS,
		Msg:       "get gas price success",
		SlowFee:   gasPrice.String() + "|" + gasTipCap.String(),                    // 慢速：基础费用
		NormalFee: gasPrice.String() + "|" + gasTipCap.String() + "|" + "*2",       // 正常：2倍优先费
		FastFee:   gasPrice.String() + "|" + gasTipCap.String() + "|" + "*3",       // 快速：3倍优先费
	}, nil
}

// SendTx 发送已签名的交易到以太坊网络
// 将原始交易数据广播到以太坊网络，返回交易哈希
// 参数:
//   - req: 包含原始交易数据的发送请求
// 返回:
//   - *account.SendTxResponse: 包含交易哈希的响应
//   - error: 如果发送失败可能返回错误
func (c *ChainAdaptor) SendTx(req *account.SendTxRequest) (*account.SendTxResponse, error) {
	// 将原始交易数据发送到以太坊网络
	transaction, err := c.ethClient.SendRawTransaction(req.RawTx)
	if err != nil {
		return &account.SendTxResponse{
			Code: common2.ReturnCode_ERROR,
			Msg:  "Send tx error" + err.Error(),
		}, err
	}
	return &account.SendTxResponse{
		Code:   common2.ReturnCode_SUCCESS,
		Msg:    "send tx success",
		TxHash: transaction.String(), // 返回交易哈希，用于后续查询交易状态
	}, nil
}

// GetTxByAddress 根据地址获取相关交易列表
// 支持查询ETH交易和ERC-20代币交易，提供分页功能
// 参数:
//   - req: 包含地址、分页信息和可选合约地址的请求
// 返回:
//   - *account.TxAddressResponse: 包含交易列表的响应
//   - error: 如果查询失败可能返回错误
func (c *ChainAdaptor) GetTxByAddress(req *account.TxAddressRequest) (*account.TxAddressResponse, error) {
	var resp *account2.TransactionResponse[account2.AccountTxResponse]
	var err error

	// 根据是否指定合约地址来决定查询类型
	if req.ContractAddress != "0x00" && req.ContractAddress != "" {
		// 查询ERC-20代币交易
		resp, err = c.ethDataClient.GetTxByAddress(uint64(req.Page), uint64(req.Pagesize), req.Address, "tokentx")
	} else {
		// 查询ETH交易
		resp, err = c.ethDataClient.GetTxByAddress(uint64(req.Page), uint64(req.Pagesize), req.Address, "txlist")
	}

	if err != nil {
		log.Error("get GetTxByAddress error", "err", err)
		return &account.TxAddressResponse{
			Code: common2.ReturnCode_ERROR,
			Msg:  "get tx list fail",
			Tx:   nil,
		}, err
	} else {
		// 转换交易数据格式为标准响应格式
		txs := resp.TransactionList
		list := make([]*account.TxMessage, 0, len(txs))
		for i := 0; i < len(txs); i++ {
			list = append(list, &account.TxMessage{
				Hash:   txs[i].TxId,              // 交易哈希
				To:     txs[i].To,                // 接收方地址
				From:   txs[i].From,              // 发送方地址
				Fee:    txs[i].TxFee,             // 交易手续费
				Status: account.TxStatus_Success, // 交易状态（这里假设都是成功的）
				Value:  txs[i].Amount,            // 交易金额
				Type:   1,                        // 交易类型
				Height: txs[i].Height,            // 区块高度
			})
		}
		fmt.Println("resp", resp)
		return &account.TxAddressResponse{
			Code: common2.ReturnCode_SUCCESS,
			Msg:  "get tx list success",
			Tx:   list,
		}, nil
	}
}

// GetTxByHash 根据交易哈希获取交易详情
// 获取交易信息和收据，支持ETH转账和ERC-20代币转账的解析
// 参数:
//   - req: 包含交易哈希的请求
// 返回:
//   - *account.TxHashResponse: 包含完整交易信息的响应
//   - error: 如果查询失败可能返回错误
func (c *ChainAdaptor) GetTxByHash(req *account.TxHashRequest) (*account.TxHashResponse, error) {
	// 根据哈希获取交易信息
	tx, err := c.ethClient.TxByHash(common.HexToHash(req.Hash))
	if err != nil {
		if errors.Is(err, ethereum.NotFound) {
			return &account.TxHashResponse{
				Code: common2.ReturnCode_ERROR,
				Msg:  "Ethereum Tx NotFound",
			}, nil
		}
		log.Error("get transaction error", "err", err)
		return &account.TxHashResponse{
			Code: common2.ReturnCode_ERROR,
			Msg:  "Ethereum Tx NotFound",
		}, nil
	}

	// 获取交易收据，包含执行结果和Gas使用情况
	receipt, err := c.ethClient.TxReceiptByHash(common.HexToHash(req.Hash))
	if err != nil {
		log.Error("get transaction receipt error", "err", err)
		return &account.TxHashResponse{
			Code: common2.ReturnCode_ERROR,
			Msg:  "Get transaction receipt error",
		}, nil
	}

	// 初始化交易解析变量
	var beforeToAddress string    // 实际接收方地址
	var beforeTokenAddress string // 代币合约地址
	var beforeValue *big.Int      // 转账金额

	// 检查目标地址是否为合约
	code, err := c.ethClient.EthGetCode(common.HexToAddress(tx.To().String()))
	if err != nil {
		log.Info("Get account code fail", "err", err)
		return nil, err
	}

	// 根据目标地址类型解析交易数据
	if code == "contract" {
		// 合约调用：解析ERC-20 transfer方法调用
		inputData := hexutil.Encode(tx.Data()[:])
		// 检查是否为ERC-20 transfer方法（方法签名：0xa9059cbb）
		if len(inputData) >= 138 && inputData[:10] == "0xa9059cbb" {
			beforeToAddress = "0x" + inputData[34:74]                    // 提取接收方地址
			trimHex := strings.TrimLeft(inputData[74:138], "0")          // 提取转账金额
			rawValue, _ := hexutil.DecodeBig("0x" + trimHex)
			beforeTokenAddress = tx.To().String()                       // 代币合约地址
			beforeValue = decimal.NewFromBigInt(rawValue, 0).BigInt()
		}
	} else {
		// 普通ETH转账
		beforeToAddress = tx.To().String()
		beforeTokenAddress = common.Address{}.String() // ETH转账没有合约地址
		beforeValue = tx.Value()
	}
	// 根据交易收据确定交易状态
	var txStatus account.TxStatus
	if receipt.Status == 1 {
		txStatus = account.TxStatus_Success // 交易成功
	} else {
		txStatus = account.TxStatus_Failed  // 交易失败
	}

	// 构造标准化的交易响应
	return &account.TxHashResponse{
		Code: common2.ReturnCode_SUCCESS,
		Msg:  "get transaction success",
		Tx: &account.TxMessage{
			Hash:            tx.Hash().Hex(),                    // 交易哈希
			Index:           uint32(receipt.TransactionIndex),   // 交易在区块中的索引
			From:            beforeTokenAddress,                 // 发送方（对于代币转账是合约地址）
			To:              beforeToAddress,                    // 接收方地址
			Value:           beforeValue.String(),               // 转账金额
			Fee:             tx.GasFeeCap().String(),            // Gas费用上限
			Status:          txStatus,                           // 交易状态
			Type:            0,                                  // 交易类型
			Height:          receipt.BlockNumber.String(),       // 区块高度
			ContractAddress: beforeTokenAddress,                 // 合约地址（如果是代币转账）
			Data:            hexutils.BytesToHex(tx.Data()),     // 交易输入数据
		},
	}, nil
}

// GetBlockByRange 获取指定范围内的区块头信息
// 批量获取连续区块的头部信息，用于区块链数据同步
// 参数:
//   - req: 包含起始和结束区块号的请求
// 返回:
//   - *account.BlockByRangeResponse: 包含区块头列表的响应
//   - error: 如果查询失败可能返回错误
func (c *ChainAdaptor) GetBlockByRange(req *account.BlockByRangeRequest) (*account.BlockByRangeResponse, error) {
	// 解析起始和结束区块号
	startBlock := new(big.Int)
	endBlock := new(big.Int)
	startBlock.SetString(req.Start, 10)
	endBlock.SetString(req.End, 10)

	// 从以太坊节点批量获取区块头信息
	// 参数：起始区块、结束区块、步长（1表示连续获取）
	blockRange, err := c.ethClient.BlockHeadersByRange(startBlock, endBlock, 1)
	if err != nil {
		log.Error("get block range fail", "err", err)
		return &account.BlockByRangeResponse{
			Code: common2.ReturnCode_ERROR,
			Msg:  "get block range fail",
		}, err
	}

	// 转换区块头数据为标准格式
	blockHeaderList := make([]*account.BlockHeader, 0, len(blockRange))
	for _, block := range blockRange {
		blockItem := &account.BlockHeader{
			ParentHash:       block.ParentHash.String(),                    // 父区块哈希
			UncleHash:        block.UncleHash.String(),                     // 叔块哈希
			CoinBase:         block.Coinbase.String(),                      // 矿工地址
			Root:             block.Root.String(),                          // 状态树根
			TxHash:           block.TxHash.String(),                        // 交易树根
			ReceiptHash:      block.ReceiptHash.String(),                   // 收据树根
			ParentBeaconRoot: block.ParentBeaconRoot.String(),              // 信标链父根
			Difficulty:       block.Difficulty.String(),                    // 挖矿难度
			Number:           block.Number.String(),                        // 区块号
			GasLimit:         block.GasLimit,                               // Gas限制
			GasUsed:          block.GasUsed,                                // 已使用Gas
			Time:             block.Time,                                   // 时间戳
			Extra:            string(block.Extra),                          // 额外数据
			MixDigest:        block.MixDigest.String(),                     // 混合哈希
			Nonce:            strconv.FormatUint(block.Nonce.Uint64(), 10), // 随机数
			BaseFee:          block.BaseFee.String(),                       // 基础费用
			WithdrawalsHash:  block.WithdrawalsHash.String(),               // 提取哈希
			BlobGasUsed:      *block.BlobGasUsed,                           // Blob Gas使用量
			ExcessBlobGas:    *block.ExcessBlobGas,                         // 过量Blob Gas
		}
		blockHeaderList = append(blockHeaderList, blockItem)
	}

	return &account.BlockByRangeResponse{
		Code:        common2.ReturnCode_SUCCESS,
		Msg:         "get block range success",
		BlockHeader: blockHeaderList,
	}, nil
}

// BuildUnSignTransaction 构建未签名的以太坊交易
// 根据交易参数构建符合EIP-1559规范的未签名交易数据
// 参数:
//   - req: 包含Base64编码交易参数的请求
// 返回:
//   - *account.UnSignTransactionResponse: 包含未签名交易数据的响应
//   - error: 如果构建失败可能返回错误
func (c *ChainAdaptor) BuildUnSignTransaction(req *account.UnSignTransactionRequest) (*account.UnSignTransactionResponse, error) {
	response := &account.UnSignTransactionResponse{
		Code: common2.ReturnCode_ERROR,
	}

	// 解析并构建动态手续费交易结构
	dFeeTx, _, err := c.buildDynamicFeeTx(req.Base64Tx)
	if err != nil {
		return nil, err
	}

	log.Info("ethereum BuildUnSignTransaction", "dFeeTx", util.ToJSONString(dFeeTx))

	// 创建EIP-1559格式的未签名交易
	// EIP-1559引入了动态手续费机制，包含基础费用和优先费用
	rawTx, err := evmbase.CreateEip1559UnSignTx(dFeeTx, dFeeTx.ChainID)
	if err != nil {
		log.Error("create un sign tx fail", "err", err)
		response.Msg = "get un sign tx fail"
		return response, nil
	}

	log.Info("ethereum BuildUnSignTransaction", "rawTx", rawTx)
	response.Code = common2.ReturnCode_SUCCESS
	response.Msg = "create un sign tx success"
	response.UnSignTx = rawTx // 返回未签名的交易数据，可用于离线签名
	return response, nil
}

// BuildSignedTransaction 构建已签名的以太坊交易
// 将签名信息与交易数据组合，生成可广播的完整交易
// 包含签名验证和发送方地址校验
// 参数:
//   - req: 包含交易参数和签名的请求
// 返回:
//   - *account.SignedTransactionResponse: 包含已签名交易数据的响应
//   - error: 如果构建或验证失败可能返回错误
func (c *ChainAdaptor) BuildSignedTransaction(req *account.SignedTransactionRequest) (*account.SignedTransactionResponse, error) {
	response := &account.SignedTransactionResponse{
		Code: common2.ReturnCode_ERROR,
	}

	// 解析交易参数，构建动态手续费交易结构
	dFeeTx, dynamicFeeTx, err := c.buildDynamicFeeTx(req.Base64Tx)
	if err != nil {
		log.Error("buildDynamicFeeTx failed", "err", err)
		return nil, err
	}

	log.Info("ethereum BuildSignedTransaction", "dFeeTx", util.ToJSONString(dFeeTx))
	log.Info("ethereum BuildSignedTransaction", "dynamicFeeTx", util.ToJSONString(dynamicFeeTx))
	log.Info("ethereum BuildSignedTransaction", "req.Signature", req.Signature)

	// 解码十六进制格式的签名数据
	inputSignatureByteList, err := hex.DecodeString(req.Signature)
	if err != nil {
		log.Error("decode signature failed", "err", err)
		return nil, fmt.Errorf("invalid signature: %w", err)
	}

	// 创建EIP-1559格式的已签名交易
	// 返回签名器、签名交易对象、原始交易数据和交易哈希
	signer, signedTx, rawTx, txHash, err := evmbase.CreateEip1559SignedTx(dFeeTx, inputSignatureByteList, dFeeTx.ChainID)
	if err != nil {
		log.Error("create signed tx fail", "err", err)
		return nil, fmt.Errorf("create signed tx fail: %w", err)
	}

	log.Info("ethereum BuildSignedTransaction", "rawTx", rawTx)

	// 从签名中恢复发送方地址，验证签名的有效性
	sender, err := types.Sender(signer, signedTx)
	if err != nil {
		log.Error("recover sender failed", "err", err)
		return nil, fmt.Errorf("recover sender failed: %w", err)
	}

	// 验证恢复的发送方地址是否与预期一致
	// 这是重要的安全检查，确保签名来自正确的私钥
	if sender.Hex() != dynamicFeeTx.FromAddress {
		log.Error("sender mismatch",
			"expected", dynamicFeeTx.FromAddress,
			"got", sender.Hex(),
		)
		return nil, fmt.Errorf("sender address mismatch: expected %s, got %s",
			dynamicFeeTx.FromAddress,
			sender.Hex(),
		)
	}

	log.Info("ethereum BuildSignedTransaction", "sender", sender.Hex())

	response.Code = common2.ReturnCode_SUCCESS
	response.Msg = txHash                // 交易哈希
	response.SignedTx = rawTx            // 已签名的原始交易数据，可直接广播
	return response, nil
}

func (c *ChainAdaptor) DecodeTransaction(req *account.DecodeTransactionRequest) (*account.DecodeTransactionResponse, error) {
	return &account.DecodeTransactionResponse{
		Code:     common2.ReturnCode_SUCCESS,
		Msg:      "verify tx success",
		Base64Tx: "0x000000",
	}, nil
}

func (c *ChainAdaptor) VerifySignedTransaction(req *account.VerifyTransactionRequest) (*account.VerifyTransactionResponse, error) {
	return &account.VerifyTransactionResponse{
		Code:   common2.ReturnCode_SUCCESS,
		Msg:    "verify tx success",
		Verify: true,
	}, nil
}

func (c *ChainAdaptor) GetExtraData(req *account.ExtraDataRequest) (*account.ExtraDataResponse, error) {
	return &account.ExtraDataResponse{
		Code:  common2.ReturnCode_SUCCESS,
		Msg:   "get extra data success",
		Value: "not data",
	}, nil
}

func (c *ChainAdaptor) GetNftListByAddress(req *account.NftAddressRequest) (*account.NftAddressResponse, error) {
	panic("implement me")
}

// buildDynamicFeeTx 构建动态费用交易的公共方法
func (c *ChainAdaptor) buildDynamicFeeTx(base64Tx string) (*types.DynamicFeeTx, *Eip1559DynamicFeeTx, error) {
	// 1. Decode base64 string
	txReqJsonByte, err := base64.StdEncoding.DecodeString(base64Tx)
	if err != nil {
		log.Error("decode string fail", "err", err)
		return nil, nil, err
	}

	// 2. Unmarshal JSON to struct
	var dynamicFeeTx Eip1559DynamicFeeTx
	if err := json.Unmarshal(txReqJsonByte, &dynamicFeeTx); err != nil {
		log.Error("parse json fail", "err", err)
		return nil, nil, err
	}

	// 3. Convert string values to big.Int
	chainID := new(big.Int)
	maxPriorityFeePerGas := new(big.Int)
	maxFeePerGas := new(big.Int)
	amount := new(big.Int)

	if _, ok := chainID.SetString(dynamicFeeTx.ChainId, 10); !ok {
		return nil, nil, fmt.Errorf("invalid chain ID: %s", dynamicFeeTx.ChainId)
	}
	if _, ok := maxPriorityFeePerGas.SetString(dynamicFeeTx.MaxPriorityFeePerGas, 10); !ok {
		return nil, nil, fmt.Errorf("invalid max priority fee: %s", dynamicFeeTx.MaxPriorityFeePerGas)
	}
	if _, ok := maxFeePerGas.SetString(dynamicFeeTx.MaxFeePerGas, 10); !ok {
		return nil, nil, fmt.Errorf("invalid max fee: %s", dynamicFeeTx.MaxFeePerGas)
	}
	if _, ok := amount.SetString(dynamicFeeTx.Amount, 10); !ok {
		return nil, nil, fmt.Errorf("invalid amount: %s", dynamicFeeTx.Amount)
	}

	// 4. Handle addresses and data
	toAddress := common.HexToAddress(dynamicFeeTx.ToAddress)
	var finalToAddress common.Address
	var finalAmount *big.Int
	var buildData []byte
	log.Info("contract address check",
		"contractAddress", dynamicFeeTx.ContractAddress,
		"isEthTransfer", isEthTransfer(&dynamicFeeTx),
	)

	// 5. Handle contract interaction vs direct transfer
	if isEthTransfer(&dynamicFeeTx) {
		finalToAddress = toAddress
		finalAmount = amount
	} else {
		contractAddress := common.HexToAddress(dynamicFeeTx.ContractAddress)
		buildData = evmbase.BuildErc20Data(toAddress, amount)
		finalToAddress = contractAddress
		finalAmount = big.NewInt(0)
	}

	// 6. Create dynamic fee transaction
	dFeeTx := &types.DynamicFeeTx{
		ChainID:   chainID,
		Nonce:     dynamicFeeTx.Nonce,
		GasTipCap: maxPriorityFeePerGas,
		GasFeeCap: maxFeePerGas,
		Gas:       dynamicFeeTx.GasLimit,
		To:        &finalToAddress,
		Value:     finalAmount,
		Data:      buildData,
	}

	return dFeeTx, &dynamicFeeTx, nil
}

// 判断是否为 ETH 转账
func isEthTransfer(tx *Eip1559DynamicFeeTx) bool {
	// 检查合约地址是否为空或零地址
	if tx.ContractAddress == "" ||
		tx.ContractAddress == "******************************************" ||
		tx.ContractAddress == "0x00" {
		return true
	}
	return false
}
