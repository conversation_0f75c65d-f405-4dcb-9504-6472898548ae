# Test for grpc api

## 1.support chain
- request
```
grpcurl -plaintext -d '{
  "chain": "Tron",
  "network": "mainet"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.getSupportChains
```
- response
```
{
  "code": "SUCCESS",
  "msg": "Support this chain",
  "support": true
}
```

## 2.convert address

- request
```
grpcurl -plaintext -d '{
  "chain": "Tron",
  "network": "mainnet",
  "publicKey": "04ff21f8e64d3a3c0198edfbb7afdc79be959432e92e2f8a1984bb436a414b8edcec0345aad0c1bf7da04fd036dd7f9f617e30669224283d950fab9dd84831dc83"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.convertAddress
```
- response

```
{
  "code": "SUCCESS",
  "msg": "convert address successs",
  "address": "TUEZSdKsoDHQMeZwihtdoBiN46zxhGWYdH"
}
```

## 3.valid address

- request
```
grpcurl -plaintext -d '{
  "chain": "Tron",
  "network": "mainnet",
  "address": "TUEZSdKsoDHQMeZwihtdoBiN46zxhGWYdH"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.validAddress
```
- response

```
{
  "code": "SUCCESS",
  "msg": "convert address success",
  "valid": true
}
```

## 4.get block by number

- request
```
grpcurl -plaintext -d '{
  "chain": "Tron",
  "height": "********"
  "viewTx": true
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.getBlockByNumber
```
- response

```
{
  "code": "SUCCESS",
  "msg": "block by number success",
  "height": "********",
  "hash": "0x0000000003f98d049ada54ba67340fb6591f027069ee4c8f64ab0f0b7bc24c36",
  "base_fee": "0x0",
  "transactions": [
    {
      "from": "",
      "to": "",
      "hash": "0x1a2ec53e0d4252453ca70af3714bbea668a1db9d6b4869615eb7f8a49023c74a",
      "amount": ""
    }
  ]
}
```

## 5.get block by hash

- request
```
grpcurl -plaintext -d '{
  "chain": "Tron",
  "hash": "0x0000000003f98d049ada54ba67340fb6591f027069ee4c8f64ab0f0b7bc24c36"
  "viewTx": true
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.getBlockByHash
```
- response

```
{
  "code": "SUCCESS",
  "msg": "block by number success",
  "height": "********",
  "hash": "0x0000000003f98d049ada54ba67340fb6591f027069ee4c8f64ab0f0b7bc24c36",
  "base_fee": "0x0",
  "transactions": [
    {
      "from": "",
      "to": "",
      "hash": "0x1a2ec53e0d4252453ca70af3714bbea668a1db9d6b4869615eb7f8a49023c74a",
      "amount": ""
    }
  ]
}
```

## 6.get account

- request
```
grpcurl -plaintext -d '{
  "address": "TFrxDg6zS459n5KUK4E48646LxRvnyZq7Z",
  "chain": "Tron"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.getAccount
```
- response

```
{
  "code": "SUCCESS",
  "msg": "get account response success",
  "network": "",
  "account_number": "0",
  "sequence": "",
  "balance": "**********"
}
```

- request
```
grpcurl -plaintext -d '{
  "chain": "Tron",
  "address": "TDYU7chCdKTEfpLaLuB9KaMgn1X9uzhZXL",
  "contractAddress": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.getAccount
```
- response

```
{
  "code": "SUCCESS",
  "msg": "get account response success",
  "network": "",
  "account_number": "",
  "sequence": "",
  "balance": "************"
}
```
## 7.get fee

- request
```
grpcurl -plaintext -d '{
  "chain": "Tron"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.getFee
```
- response

```
{
  "code": "ERROR",
  "msg": "oklink scan server: This chain does not currently support.",
  "slow_fee": "",
  "normal_fee": "",
  "fast_fee": ""
}
```

## 8.send tx

- request
```
grpcurl -plaintext -d '{
  "chain": "Tron",
  "rawTx": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.SendTx
```
- response

```
{
  "code": "SUCCESS",
  "msg": "send tx success",
  "tx_hash": "7e9dfd64d06ad2328b770ec027bed5d3a1b11a93861583129da9c2f652614d07"
}
```

## 9.get tx by address

- request
```
grpcurl -plaintext -d '{
  "chain": "Tron",
  "address": "TT2T17KZhoDu47i2E4FWxfG79zdkEWkU9N"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.getTxByAddress
```
- response

```
{
  "code": "SUCCESS",
  "msg": "get transactions by address success",
  "tx": [
    {
      "hash": "ba03d46d2c7eb444520a415a5d49b48d97571946575bfd5e49a8e52639182166",
      "index": 0,
      "froms": [
        {
          "address": "TT2T17KZhoDu47i2E4FWxfG79zdkEWkU9N"
        }
      ],
      "tos": [
        {
          "address": "TVFBd9TBHkk4L6FEfjQ5Fa1XABTfEdzqkp"
        }
      ],
      "fee": "ba03d46d2c7eb444520a415a5d49b48d97571946575bfd5e49a8e52639182166",
      "status": "Success",
      "values": [
        {
          "value": "************"
        }
      ],
      "type": 1,
      "height": "********",
      "contract_address": "",
      "datetime": "",
      "data": ""
    },
    {
      "hash": "4fed1b1616477d65b6bdad61d64b84d64f909ecb1dd042ad9b3122b345a10c8f",
      "index": 0,
      "froms": [
        {
          "address": "TT2T17KZhoDu47i2E4FWxfG79zdkEWkU9N"
        }
      ],
      "tos": [
        {
          "address": "TFYtVqCrEizVWcLhVCZPRUDYLLBgqV7jAo"
        }
      ],
      "fee": "4fed1b1616477d65b6bdad61d64b84d64f909ecb1dd042ad9b3122b345a10c8f",
      "status": "Success",
      "values": [
        {
          "value": "5000000"
        }
      ],
      "type": 1,
      "height": "66595297",
      "contract_address": "",
      "datetime": "",
      "data": ""
    },
    {
      "hash": "b1a8295adb72a0b8341ca381af57ee83b358cbb33b2c3a6a3bc82ca20d556603",
      "index": 0,
      "froms": [
        {
          "address": "TXJgMdjVX5dKiQaUi9QobwNxtSQaFqccvd"
        }
      ],
      "tos": [
        {
          "address": "TT2T17KZhoDu47i2E4FWxfG79zdkEWkU9N"
        }
      ],
      "fee": "b1a8295adb72a0b8341ca381af57ee83b358cbb33b2c3a6a3bc82ca20d556603",
      "status": "Success",
      "values": [
        {
          "value": "5000000"
        }
      ],
      "type": 1,
      "height": "66595270",
      "contract_address": "",
      "datetime": "",
      "data": ""
    },
    {
      "hash": "b1a8295adb72a0b8341ca381af57ee83b358cbb33b2c3a6a3bc82ca20d556603",
      "index": 0,
      "froms": [
        {
          "address": "TT2T17KZhoDu47i2E4FWxfG79zdkEWkU9N"
        }
      ],
      "tos": [
        {
          "address": "TXJgMdjVX5dKiQaUi9QobwNxtSQaFqccvd"
        }
      ],
      "fee": "b1a8295adb72a0b8341ca381af57ee83b358cbb33b2c3a6a3bc82ca20d556603",
      "status": "Success",
      "values": [
        {
          "value": "481899565.4658516"
        }
      ],
      "type": 1,
      "height": "66595270",
      "contract_address": "",
      "datetime": "",
      "data": ""
    },
    {
      "hash": "b377524ac3cfe24bf2e9aef30c793c3d9b9c9932ced980a7043f3e3b0413b613",
      "index": 0,
      "froms": [
        {
          "address": "TT2T17KZhoDu47i2E4FWxfG79zdkEWkU9N"
        }
      ],
      "tos": [
        {
          "address": "TPxZQTZQDthfsPCV6RUo2NEvv41ahTFmSX"
        }
      ],
      "fee": "b377524ac3cfe24bf2e9aef30c793c3d9b9c9932ced980a7043f3e3b0413b613",
      "status": "Success",
      "values": [
        {
          "value": "2000000"
        }
      ],
      "type": 1,
      "height": "66594908",
      "contract_address": "",
      "datetime": "",
      "data": ""
    },
    {
      "hash": "5076c12c03c5cbac0af935eb66f4f22ec0baeb084c16001cb5b979f3033e53cb",
      "index": 0,
      "froms": [
        {
          "address": "TXJgMdjVX5dKiQaUi9QobwNxtSQaFqccvd"
        }
      ],
      "tos": [
        {
          "address": "TT2T17KZhoDu47i2E4FWxfG79zdkEWkU9N"
        }
      ],
      "fee": "5076c12c03c5cbac0af935eb66f4f22ec0baeb084c16001cb5b979f3033e53cb",
      "status": "Success",
      "values": [
        {
          "value": "2000000"
        }
      ],
      "type": 1,
      "height": "66594884",
      "contract_address": "",
      "datetime": "",
      "data": ""
    },
    {
      "hash": "5076c12c03c5cbac0af935eb66f4f22ec0baeb084c16001cb5b979f3033e53cb",
      "index": 0,
      "froms": [
        {
          "address": "TT2T17KZhoDu47i2E4FWxfG79zdkEWkU9N"
        }
      ],
      "tos": [
        {
          "address": "TXJgMdjVX5dKiQaUi9QobwNxtSQaFqccvd"
        }
      ],
      "fee": "5076c12c03c5cbac0af935eb66f4f22ec0baeb084c16001cb5b979f3033e53cb",
      "status": "Success",
      "values": [
        {
          "value": "192759934.18067276"
        }
      ],
      "type": 1,
      "height": "66594884",
      "contract_address": "",
      "datetime": "",
      "data": ""
    },
    {
      "hash": "dd323e15041629ad80a0c48c752ba27c0b5dcbe10295a5442413adf3fa41f34f",
      "index": 0,
      "froms": [
        {
          "address": "TT2T17KZhoDu47i2E4FWxfG79zdkEWkU9N"
        }
      ],
      "tos": [
        {
          "address": "TEKr38LtF16yWNAoh8RwNBSpyVD7YQDV92"
        }
      ],
      "fee": "dd323e15041629ad80a0c48c752ba27c0b5dcbe10295a5442413adf3fa41f34f",
      "status": "Success",
      "values": [
        {
          "value": "500000"
        }
      ],
      "type": 1,
      "height": "66578493",
      "contract_address": "",
      "datetime": "",
      "data": ""
    },
    {
      "hash": "b669b3be7783dbb772e9f6e2d6fd0a49c53e13b52a671327be466050150a119e",
      "index": 0,
      "froms": [
        {
          "address": "TXJgMdjVX5dKiQaUi9QobwNxtSQaFqccvd"
        }
      ],
      "tos": [
        {
          "address": "TT2T17KZhoDu47i2E4FWxfG79zdkEWkU9N"
        }
      ],
      "fee": "b669b3be7783dbb772e9f6e2d6fd0a49c53e13b52a671327be466050150a119e",
      "status": "Success",
      "values": [
        {
          "value": "500000"
        }
      ],
      "type": 1,
      "height": "66578411",
      "contract_address": "",
      "datetime": "",
      "data": ""
    },
    {
      "hash": "b669b3be7783dbb772e9f6e2d6fd0a49c53e13b52a671327be466050150a119e",
      "index": 0,
      "froms": [
        {
          "address": "TT2T17KZhoDu47i2E4FWxfG79zdkEWkU9N"
        }
      ],
      "tos": [
        {
          "address": "TXJgMdjVX5dKiQaUi9QobwNxtSQaFqccvd"
        }
      ],
      "fee": "b669b3be7783dbb772e9f6e2d6fd0a49c53e13b52a671327be466050150a119e",
      "status": "Success",
      "values": [
        {
          "value": "48191047.98850116"
        }
      ],
      "type": 1,
      "height": "66578411",
      "contract_address": "",
      "datetime": "",
      "data": ""
    },
    {
      "hash": "99499b2b60373033443a9440a2338e1efaef86b1275b5ec93f028dd8d1cc293d",
      "index": 0,
      "froms": [
        {
          "address": "TVrZ3PjjFGbnp44p6SGASAKrJWAUjCHmCA"
        }
      ],
      "tos": [
        {
          "address": "TT2T17KZhoDu47i2E4FWxfG79zdkEWkU9N"
        }
      ],
      "fee": "99499b2b60373033443a9440a2338e1efaef86b1275b5ec93f028dd8d1cc293d",
      "status": "Success",
      "values": [
        {
          "value": "4.0884333774500305"
        }
      ],
      "type": 1,
      "height": "66486830",
      "contract_address": "",
      "datetime": "",
      "data": ""
    },
    {
      "hash": "777c32383730b462c84754b3fd02b870f5625416964ac49bfb41ce5a8e392933",
      "index": 0,
      "froms": [
        {
          "address": "TVrZ3PjjFGbnp44p6SGASAKrJWAUjCHmCA"
        }
      ],
      "tos": [
        {
          "address": "TT2T17KZhoDu47i2E4FWxfG79zdkEWkU9N"
        }
      ],
      "fee": "777c32383730b462c84754b3fd02b870f5625416964ac49bfb41ce5a8e392933",
      "status": "Success",
      "values": [
        {
          "value": "4.3298073632057905"
        }
      ],
      "type": 1,
      "height": "66486797",
      "contract_address": "",
      "datetime": "",
      "data": ""
    },
    {
      "hash": "f0650fd6def14fab9b1b4eb477a7086763b6a736aa3d176007995e74c8247d5a",
      "index": 0,
      "froms": [
        {
          "address": "TVrZ3PjjFGbnp44p6SGASAKrJWAUjCHmCA"
        }
      ],
      "tos": [
        {
          "address": "TT2T17KZhoDu47i2E4FWxfG79zdkEWkU9N"
        }
      ],
      "fee": "f0650fd6def14fab9b1b4eb477a7086763b6a736aa3d176007995e74c8247d5a",
      "status": "Success",
      "values": [
        {
          "value": "4.3575634841998685"
        }
      ],
      "type": 1,
      "height": "66486775",
      "contract_address": "",
      "datetime": "",
      "data": ""
    },
    {
      "hash": "3f89754bd7308f5cf22a52289be7b4014a7a825595192d0e9b76f0fbe4cbb06f",
      "index": 0,
      "froms": [
        {
          "address": "TVrZ3PjjFGbnp44p6SGASAKrJWAUjCHmCA"
        }
      ],
      "tos": [
        {
          "address": "TT2T17KZhoDu47i2E4FWxfG79zdkEWkU9N"
        }
      ],
      "fee": "3f89754bd7308f5cf22a52289be7b4014a7a825595192d0e9b76f0fbe4cbb06f",
      "status": "Success",
      "values": [
        {
          "value": "2.3393425765189955"
        }
      ],
      "type": 1,
      "height": "66486764",
      "contract_address": "",
      "datetime": "",
      "data": ""
    },
    {
      "hash": "5293b44f5d6477fee3975c7fb4c036e3fb23a0c4e227257021362071e4edf3bf",
      "index": 0,
      "froms": [
        {
          "address": "TVrZ3PjjFGbnp44p6SGASAKrJWAUjCHmCA"
        }
      ],
      "tos": [
        {
          "address": "TT2T17KZhoDu47i2E4FWxfG79zdkEWkU9N"
        }
      ],
      "fee": "5293b44f5d6477fee3975c7fb4c036e3fb23a0c4e227257021362071e4edf3bf",
      "status": "Success",
      "values": [
        {
          "value": "2.3890272692660837"
        }
      ],
      "type": 1,
      "height": "66486752",
      "contract_address": "",
      "datetime": "",
      "data": ""
    },
    {
      "hash": "3bc8be0ddb5d94151e9f9dcc7ff3550a79d53dcc1ca8ea581e12e1f2aa63e811",
      "index": 0,
      "froms": [
        {
          "address": "TVrZ3PjjFGbnp44p6SGASAKrJWAUjCHmCA"
        }
      ],
      "tos": [
        {
          "address": "TT2T17KZhoDu47i2E4FWxfG79zdkEWkU9N"
        }
      ],
      "fee": "3bc8be0ddb5d94151e9f9dcc7ff3550a79d53dcc1ca8ea581e12e1f2aa63e811",
      "status": "Success",
      "values": [
        {
          "value": "2.515041871032717"
        }
      ],
      "type": 1,
      "height": "66486739",
      "contract_address": "",
      "datetime": "",
      "data": ""
    },
    {
      "hash": "eb3a7227cebaebc41d63a31d4c75da42eda65761f524a79e72aec8e5fca0bfc2",
      "index": 0,
      "froms": [
        {
          "address": "TVrZ3PjjFGbnp44p6SGASAKrJWAUjCHmCA"
        }
      ],
      "tos": [
        {
          "address": "TT2T17KZhoDu47i2E4FWxfG79zdkEWkU9N"
        }
      ],
      "fee": "eb3a7227cebaebc41d63a31d4c75da42eda65761f524a79e72aec8e5fca0bfc2",
      "status": "Success",
      "values": [
        {
          "value": "2.3410471093982603"
        }
      ],
      "type": 1,
      "height": "66486708",
      "contract_address": "",
      "datetime": "",
      "data": ""
    },
    {
      "hash": "e6be89b58006d29e799df62a33895cbd5b56e6417f517c7673da875dee501f28",
      "index": 0,
      "froms": [
        {
          "address": "TVrZ3PjjFGbnp44p6SGASAKrJWAUjCHmCA"
        }
      ],
      "tos": [
        {
          "address": "TT2T17KZhoDu47i2E4FWxfG79zdkEWkU9N"
        }
      ],
      "fee": "e6be89b58006d29e799df62a33895cbd5b56e6417f517c7673da875dee501f28",
      "status": "Success",
      "values": [
        {
          "value": "1.272241317215229"
        }
      ],
      "type": 1,
      "height": "66486697",
      "contract_address": "",
      "datetime": "",
      "data": ""
    },
    {
      "hash": "d04743c646fc9e7ae67978a3b599a39efa0b9b6acb2d468c8ad801f10c903d8b",
      "index": 0,
      "froms": [
        {
          "address": "TVrZ3PjjFGbnp44p6SGASAKrJWAUjCHmCA"
        }
      ],
      "tos": [
        {
          "address": "TT2T17KZhoDu47i2E4FWxfG79zdkEWkU9N"
        }
      ],
      "fee": "d04743c646fc9e7ae67978a3b599a39efa0b9b6acb2d468c8ad801f10c903d8b",
      "status": "Success",
      "values": [
        {
          "value": "1.3220897889615808"
        }
      ],
      "type": 1,
      "height": "66486690",
      "contract_address": "",
      "datetime": "",
      "data": ""
    },
    {
      "hash": "d88ade8349c5fa550e2f0d6e440397b2790af2be95aff2a800f36c5a71ae7507",
      "index": 0,
      "froms": [
        {
          "address": "TVrZ3PjjFGbnp44p6SGASAKrJWAUjCHmCA"
        }
      ],
      "tos": [
        {
          "address": "TT2T17KZhoDu47i2E4FWxfG79zdkEWkU9N"
        }
      ],
      "fee": "d88ade8349c5fa550e2f0d6e440397b2790af2be95aff2a800f36c5a71ae7507",
      "status": "Success",
      "values": [
        {
          "value": "1.****************"
        }
      ],
      "type": 1,
      "height": "********",
      "contract_address": "",
      "datetime": "",
      "data": ""
    }
  ]
}
```

## 10.get tx by hash

- request
```
grpcurl -plaintext -d '{
  "chain": "Tron",
  "hash": "d88ade8349c5fa550e2f0d6e440397b2790af2be95aff2a800f36c5a71ae7507"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.getTxByHash
```
- response

```
{
  "code": "SUCCESS",
  "msg": "get transactions by address success",
  "tx": {
    "hash": "6d721169d54e98b31d527c204b7a6eb7f2a63815dae43d998be4f46025d19860",
    "index": 0,
    "froms": [
      {
        "address": "TW5TJk9CmaDeCNmV7HLm6LWw7wBhABBBBB"
      }
    ],
    "tos": [
      {
        "address": "TKdTmQFhhGsP3b4xbvuhgLpQjUQfRFFFFF"
      }
    ],
    "fee": "",
    "status": "Success",
    "values": [
      {
        "value": "177000"
      }
    ],
    "type": 1,
    "height": "",
    "contract_address": "",
    "datetime": "",
    "data": ""
  }
}
```

## 11.get block by range

- request
```
grpcurl -plaintext -d '{
  "chain": "Tron",
  "start": "********",
  "end": "********"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.getBlockHeaderByRange
```
- response

```
{
  "code": "SUCCESS",
  "msg": "get block by range success",
  "block_header": [
    {
      "hash": "",
      "parent_hash": "0x0000000003f98f83179064047468e091a4cce804763fdf39cb83fe65ea045584",
      "uncle_hash": "",
      "coin_base": "",
      "root": "",
      "tx_hash": "",
      "receipt_hash": "",
      "parent_beacon_root": "",
      "difficulty": "0x0",
      "number": "0x3f98f84",
      "gas_limit": "0",
      "gas_used": "0",
      "time": "0",
      "extra": "",
      "mix_digest": "",
      "nonce": "0x0000000000000000",
      "base_fee": "",
      "withdrawals_hash": "",
      "blob_gas_used": "0",
      "excess_blob_gas": "0"
    },
    {
      "hash": "",
      "parent_hash": "0x0000000003f98f84b6763e4c9402f074e72e30d0987e9d0fc57cc1278f67dbf2",
      "uncle_hash": "",
      "coin_base": "",
      "root": "",
      "tx_hash": "",
      "receipt_hash": "",
      "parent_beacon_root": "",
      "difficulty": "0x0",
      "number": "0x3f98f85",
      "gas_limit": "0",
      "gas_used": "0",
      "time": "0",
      "extra": "",
      "mix_digest": "",
      "nonce": "0x0000000000000000",
      "base_fee": "",
      "withdrawals_hash": "",
      "blob_gas_used": "0",
      "excess_blob_gas": "0"
    },
    {
      "hash": "",
      "parent_hash": "0x0000000003f98f8555ab767fe1b718dd921587e439f13042073ec502cdedab64",
      "uncle_hash": "",
      "coin_base": "",
      "root": "",
      "tx_hash": "",
      "receipt_hash": "",
      "parent_beacon_root": "",
      "difficulty": "0x0",
      "number": "0x3f98f86",
      "gas_limit": "0",
      "gas_used": "0",
      "time": "0",
      "extra": "",
      "mix_digest": "",
      "nonce": "0x0000000000000000",
      "base_fee": "",
      "withdrawals_hash": "",
      "blob_gas_used": "0",
      "excess_blob_gas": "0"
    },
    {
      "hash": "",
      "parent_hash": "0x0000000003f98f862aa9d3624b57c0d9ad6cf16014c84bdd4ab2e961ef97cd99",
      "uncle_hash": "",
      "coin_base": "",
      "root": "",
      "tx_hash": "",
      "receipt_hash": "",
      "parent_beacon_root": "",
      "difficulty": "0x0",
      "number": "0x3f98f87",
      "gas_limit": "0",
      "gas_used": "0",
      "time": "0",
      "extra": "",
      "mix_digest": "",
      "nonce": "0x0000000000000000",
      "base_fee": "",
      "withdrawals_hash": "",
      "blob_gas_used": "0",
      "excess_blob_gas": "0"
    },
    {
      "hash": "",
      "parent_hash": "0x0000000003f98f87feb02444d4f6f073c84e0c72c945c5de6215027dcaec0c20",
      "uncle_hash": "",
      "coin_base": "",
      "root": "",
      "tx_hash": "",
      "receipt_hash": "",
      "parent_beacon_root": "",
      "difficulty": "0x0",
      "number": "0x3f98f88",
      "gas_limit": "0",
      "gas_used": "0",
      "time": "0",
      "extra": "",
      "mix_digest": "",
      "nonce": "0x0000000000000000",
      "base_fee": "",
      "withdrawals_hash": "",
      "blob_gas_used": "0",
      "excess_blob_gas": "0"
    },
    {
      "hash": "",
      "parent_hash": "0x0000000003f98f88bef14f2b2a22e9486350118f6584d37d8dc7fbadde668451",
      "uncle_hash": "",
      "coin_base": "",
      "root": "",
      "tx_hash": "",
      "receipt_hash": "",
      "parent_beacon_root": "",
      "difficulty": "0x0",
      "number": "0x3f98f89",
      "gas_limit": "0",
      "gas_used": "0",
      "time": "0",
      "extra": "",
      "mix_digest": "",
      "nonce": "0x0000000000000000",
      "base_fee": "",
      "withdrawals_hash": "",
      "blob_gas_used": "0",
      "excess_blob_gas": "0"
    },
    {
      "hash": "",
      "parent_hash": "0x0000000003f98f89a7299370bfbbb6cab2da2ad81f1ccab35fa8045fa6de1f63",
      "uncle_hash": "",
      "coin_base": "",
      "root": "",
      "tx_hash": "",
      "receipt_hash": "",
      "parent_beacon_root": "",
      "difficulty": "0x0",
      "number": "0x3f98f8a",
      "gas_limit": "0",
      "gas_used": "0",
      "time": "0",
      "extra": "",
      "mix_digest": "",
      "nonce": "0x0000000000000000",
      "base_fee": "",
      "withdrawals_hash": "",
      "blob_gas_used": "0",
      "excess_blob_gas": "0"
    },
    {
      "hash": "",
      "parent_hash": "0x0000000003f98f8abbd8394c7fde436ea5618bd9dc4852dc949595ca880be258",
      "uncle_hash": "",
      "coin_base": "",
      "root": "",
      "tx_hash": "",
      "receipt_hash": "",
      "parent_beacon_root": "",
      "difficulty": "0x0",
      "number": "0x3f98f8b",
      "gas_limit": "0",
      "gas_used": "0",
      "time": "0",
      "extra": "",
      "mix_digest": "",
      "nonce": "0x0000000000000000",
      "base_fee": "",
      "withdrawals_hash": "",
      "blob_gas_used": "0",
      "excess_blob_gas": "0"
    },
    {
      "hash": "",
      "parent_hash": "0x0000000003f98f8b336f9debed985942c75682fc263d0a4ee32c6d7627122dc3",
      "uncle_hash": "",
      "coin_base": "",
      "root": "",
      "tx_hash": "",
      "receipt_hash": "",
      "parent_beacon_root": "",
      "difficulty": "0x0",
      "number": "0x3f98f8c",
      "gas_limit": "0",
      "gas_used": "0",
      "time": "0",
      "extra": "",
      "mix_digest": "",
      "nonce": "0x0000000000000000",
      "base_fee": "",
      "withdrawals_hash": "",
      "blob_gas_used": "0",
      "excess_blob_gas": "0"
    },
    {
      "hash": "",
      "parent_hash": "0x0000000003f98f8cb8176d124e6bb1ab56631f69d535ccdc13057f6548bb57e3",
      "uncle_hash": "",
      "coin_base": "",
      "root": "",
      "tx_hash": "",
      "receipt_hash": "",
      "parent_beacon_root": "",
      "difficulty": "0x0",
      "number": "0x3f98f8d",
      "gas_limit": "0",
      "gas_used": "0",
      "time": "0",
      "extra": "",
      "mix_digest": "",
      "nonce": "0x0000000000000000",
      "base_fee": "",
      "withdrawals_hash": "",
      "blob_gas_used": "0",
      "excess_blob_gas": "0"
    },
    {
      "hash": "",
      "parent_hash": "0x0000000003f98f8d6e42d7167ae2881f796c96048e3c5c0c1864fdf4fcfdb155",
      "uncle_hash": "",
      "coin_base": "",
      "root": "",
      "tx_hash": "",
      "receipt_hash": "",
      "parent_beacon_root": "",
      "difficulty": "0x0",
      "number": "0x3f98f8e",
      "gas_limit": "0",
      "gas_used": "0",
      "time": "0",
      "extra": "",
      "mix_digest": "",
      "nonce": "0x0000000000000000",
      "base_fee": "",
      "withdrawals_hash": "",
      "blob_gas_used": "0",
      "excess_blob_gas": "0"
    }
  ]
}
```

## 12.create un sign transaction

- request
```
grpcurl -plaintext -d '{
  "chain": "Tron",
  "base64Tx": "ewoJImZyb21fYWRkcmVzcyI6ICJUSDM2SzVWUjJGNkR4ZWZ6dHU5QTNMTmFKc2RqeUdSRGJpIiwKCSJ0b19hZGRyZXNzIjogIlRCaVNrZEZUUTJmQzg4dWczaExXb1ZITThGakRwOHF5RDIiLAoJInZhbHVlIjogMQp9"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.BuildUnSignTransaction

grpcurl -plaintext -d '{
  "chain": "Tron",
  "base64Tx": "ewoJImZyb21fYWRkcmVzcyI6ICJUSDM2SzVWUjJGNkR4ZWZ6dHU5QTNMTmFKc2RqeUdSRGJpIiwKCSJ0b19hZGRyZXNzIjogIlRCaVNrZEZUUTJmQzg4dWczaExXb1ZITThGakRwOHF5RDIiLAoJImNvbnRyYWN0X2FkZHJlc3MiOiAiVFI3TkhxamVLUXhHVENpOHE4Wlk0cEw4b3RTemdqTGo2dCIsCgkidmFsdWUiOiAxCn0="
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.BuildUnSignTransaction
```
- response

```
{
  "code": "SUCCESS",
  "msg": "create un sign tx success",
  "un_sign_tx": "0a02907f2208a52b4407680520a540c8f59bb3af325a65080112610a2d747970652e676f6f676c65617069732e636f6d2f70726f746f636f6c2e5472616e73666572436f6e747261637412300a15414d84f5daa3f50ac6ac2893d45faea7cf7ad9532b12154113257db3c2624838b25fdfd28e65bdaebbd005c318017099ab98b3af32"
}
```