# Test for grpc api

## 1.support chain
- request
```
grpcurl -plaintext -d '{
  "chain": "Polygon",
  "network": "mainnet"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.getSupportChains
```
- response
```
{
  "code": "SUCCESS",
  "msg": "Support this chain",
  "support": true
}
```

## 2.convert address

- request
```
grpcurl -plaintext -d '{
  "chain": "Polygon",
  "network": "mainnet",
  "publicKey": "02e993166ac8fb56c438a2a0e1266f33b54dfe7b79f738d9945dbbbebf6e367c55"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.convertAddress
```
- reponse

```
{
  "code": "SUCCESS",
  "msg": "convert address success",
  "address": "******************************************"
}
```

## 3.valid address

- request
```
grpcurl -plaintext -d '{
  "chain": "Polygon",
  "network": "mainnet",
  "address": "******************************************"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.validAddress
```
- response
```
{
  "code": "SUCCESS",
  "msg": "valid address",
  "valid": true
}
```

## latest block header by number

- request
```
grpcurl -plaintext -d '{
  "height": "********",
  "network": "mainnet",
  "chain": "Polygon"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.getBlockHeaderByNumber
```
- reponse
```
{
  "code": "SUCCESS",
  "msg": "get latest block header success",
  "block_header": {
    "hash": "0xbd56b33a34ce67fa1bee83da0c0135f16af5296b2d6ff97750f76f52c67eceb6",
    "parent_hash": "0x4eaf920bac6fd9ba324b18bf702e5f36611c076431483441d30b5096a6d01b70",
    "uncle_hash": "0x1dcc4de8dec75d7aab85b567b6ccd41ad312451b948a7413f0a142fd40d49347",
    "coin_base": "******************************************",
    "root": "0xc3f596cfbc88cb3f9904dffac9e1fafb84fed7a4414e362d3821ba7a26f88d3f",
    "tx_hash": "0x6a16bab64d359552d370abe5e5a6e448b16888927112b672ceaf514ce0baefb8",
    "receipt_hash": "0xa39b18404dc71fac087e1467288ba608b09979cd9fa2440812f27a07b63bc3fd",
    "parent_beacon_root": "******************************************000000000000000000000000",
    "difficulty": "20",
    "number": "********",
    "gas_limit": "********",
    "gas_used": "********",
    "time": "**********",
    "extra": "d78301050383626f7288676f312e32322e31856c696e75780000000000000000f88d80f88ac0c0c0c0c0c0c0c0c0c0c0c108c102c0c2060cc10ec10fc110c0c0c112c0c0c116c0c2050dc10bc11ac111c0c11bc0c11fc11ec121c122c123c124c125c126c0c127c129c12ac12bc12cc12dc12ec0c11cc119c0c0c22f34c135c0c135c131c138c13ac3323739c114c119c13cc13bc131c13cc128c24042c144c145c146c147c148c149c14ac14bc14c913ba96c8c0b34bc035731843b47ed3676525b32f2874ed5cca2025a49d8ffaf0b8856e591ba6e88f1f3f484a2cd6351541c0d3f86feefc69abbed568acde5bf01",
    "mix_digest": "******************************************000000000000000000000000",
    "nonce": "0",
    "base_fee": "**********",
    "withdrawals_hash": "******************************************000000000000000000000000",
    "blob_gas_used": "0",
    "excess_blob_gas": "0"
  }
}
```

## block header by hash

- request
```
grpcurl -plaintext -d '{
  "chain": "Polygon",
  "network": "mainnet",
  "hash": "0xbd56b33a34ce67fa1bee83da0c0135f16af5296b2d6ff97750f76f52c67eceb6"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.getBlockHeaderByHash
```

- response
```
{
  "code": "SUCCESS",
  "msg": "get block header success",
  "block_header": {
    "hash": "0xbd56b33a34ce67fa1bee83da0c0135f16af5296b2d6ff97750f76f52c67eceb6",
    "parent_hash": "0x4eaf920bac6fd9ba324b18bf702e5f36611c076431483441d30b5096a6d01b70",
    "uncle_hash": "0x1dcc4de8dec75d7aab85b567b6ccd41ad312451b948a7413f0a142fd40d49347",
    "coin_base": "******************************************",
    "root": "0xc3f596cfbc88cb3f9904dffac9e1fafb84fed7a4414e362d3821ba7a26f88d3f",
    "tx_hash": "0x6a16bab64d359552d370abe5e5a6e448b16888927112b672ceaf514ce0baefb8",
    "receipt_hash": "0xa39b18404dc71fac087e1467288ba608b09979cd9fa2440812f27a07b63bc3fd",
    "parent_beacon_root": "******************************************000000000000000000000000",
    "difficulty": "20",
    "number": "********",
    "gas_limit": "********",
    "gas_used": "********",
    "time": "**********",
    "extra": "d78301050383626f7288676f312e32322e31856c696e75780000000000000000f88d80f88ac0c0c0c0c0c0c0c0c0c0c0c108c102c0c2060cc10ec10fc110c0c0c112c0c0c116c0c2050dc10bc11ac111c0c11bc0c11fc11ec121c122c123c124c125c126c0c127c129c12ac12bc12cc12dc12ec0c11cc119c0c0c22f34c135c0c135c131c138c13ac3323739c114c119c13cc13bc131c13cc128c24042c144c145c146c147c148c149c14ac14bc14c913ba96c8c0b34bc035731843b47ed3676525b32f2874ed5cca2025a49d8ffaf0b8856e591ba6e88f1f3f484a2cd6351541c0d3f86feefc69abbed568acde5bf01",
    "mix_digest": "******************************************000000000000000000000000",
    "nonce": "0",
    "base_fee": "**********",
    "withdrawals_hash": "******************************************000000000000000000000000",
    "blob_gas_used": "0",
    "excess_blob_gas": "0"
  }
}
```

## block by number 
- request
```
grpcurl -plaintext -d '{
  "height": "********",
  "chain": "Polygon"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.getBlockByNumber


```
- response
```
{
  "code": "SUCCESS",
  "msg": "block by number success",
  "height": "0",
  "hash": "0x62a8a1b68f5cceedca1b28a8a393b8b3f8c49be5b9f297d3674fa836f8ba21b6",
  "base_fee": "",
  "transactions": [
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0xe742ca7a7ffc80682b9b16f83d7d22b232d4f1939bda03bee622631165105b91",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0xbd56449a2d614b21454d2c1a34cc2bd7a2747d1a387387565fe1c0d1718f5382",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0xb5bf7afeecdd60c89ba9255b1cd0a5bb6e8a7c27be48f0e1ff21081c089adf7b",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x346795cd141dbe397abaf9cd9cd10dd05a2941bdfb336f868ca52ee2eb0792b2",
      "height": "0",
      "amount": "0x9fdf42f6e48000"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x5e45b17505818959a71f63360e38f1b889dcbf64842b450cf7e637888c1e6cf4",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x55fa85238a268eb6dade432d3228eea17cad944e5cfb19e2aedc1c1b96f22f93",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x899693abaf9a55abb38af9c4e192163032117d3ce59ea3a7cd35b2b92e914990",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0xfa09970421774a405630da0fe0ae1624b842707379aabe9e6d72565ee46b972e",
      "height": "0",
      "amount": "0x357c9225a1fe36eace"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0xfacb7bd82f364e71c4651b41728fb41d9e706840f231d4b020ebd08ed722e93c",
      "height": "0",
      "amount": "0x3782dace9d900000"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x4cb2f3266431c068c174bbad661bd11033b352550b321aeae9daca2c33133352",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0xd5228e7f5ad9143d4d1c878997a5d46735daf398c4558b708451a2f686d057ab",
      "height": "0",
      "amount": "0x9a63f08ea63880000"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x203ba36531013e4a540b171ebbbe3d3127d5ad3560fb4627e148aacb44d925f2",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x6cc517e7c44dc669a5636bc045000b0a4a1c5bc2dc03d6b60ae36c2bd74c76b2",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x299c06d7a27b4ae8a9264a269437f70d0e5dda472d66837f32277b5ecf02de31",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x7d1a7e62d4fc6c937ab7f58e20dea6f92c8b508616aaea45f729eb0bc525f930",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x697bd661895d4296f5e88006c8ea15063b2c5f53fb2babe154ed2584dad20728",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x084759bf20b5761002d3704b2ee39686a4dda53dedf95a6a941118f0637bd3b3",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0xbe69e09856cffbd84209381d7d749f36a55145f8740177c4d216fb907c159f93",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x53c8d70ee5fc2a7e07b779b5d39946a0a9e929e6dec2114ee106ab7685fb281a",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0xe3fed51e6870de3d585da27a90021bb58062c2d39687fdb56773e6cc7571fa40",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0xa2ad7fb80259772e6031e22579321cd80e25b2b436af33fb8129c3fbe451e08d",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x27414a2c7e5212294dd78c8038007ab9e9d7853f782867f5fe2226876b30ae86",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x625ddc9cf4ba3650acb8dc15ae33c5f92b1cd6ae0439e5396006fe2a9d2f3efe",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0xa4b0da7a071fb8eae32bfafcfe2aed39354b35e9e6dea07f4e8e4ef8b08088ce",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x45eba20f1861aeaebb17dcb89b39d435293a9be80b85b87c705c89df6f090258",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0xb2df4925ae30076eb0cf24ecb1b18836b4af2acc901e2ece75dbf22723c0c711",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x6b2d8222dbc9392088662d577fb84b9133662b875cc96f175a486a4c30f83d81",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x8cb7cd6f87e0bb2ad5c66a8f574200da8ae9b5be087d788fbd09536a8063d2a5",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x7615a43e923cab1953467bef804fc7e3f4e14bf94f5a607968893923e89e38cb",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x5630e60ff0043594c9c403f30d42b1a349706fd041c401277e69defe94edfeeb",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x703fc16f47724c8d874c805b54fc07fc661f2c2ca9885076374850d4d60e0b33",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x39035d4332a6b753c586f00ffb51c863a9556686f509a8eaaf2a22ab7a45b5c8",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x329fa57e68b5e6b10d1d0a1b04bfc5438742b51abe53f1a5e34806d84422b81e",
      "height": "0",
      "amount": "0x5af3107a4000"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x07da90ec297596ffd9ac98a1276fc40fbb31aea2108b47915d68a2fab95cf1a3",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x0c7cda23b669ea09f37cc7a27ba6d630ef47475ce1dd59e559bc680d0cdf020b",
      "height": "0",
      "amount": "0xaa87bee538000"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x4614092e5bafedcc6c358c9e9222e3071c7fcb2245d264e3a3ec1a761a774a5b",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0xe8d87faa91634ffeb833546ec8d8bfbe711b992043b2af5f4565a8e9f6460092",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0xfb769c144f25154ff576988f93fbaab56dd63402c0e4ec23647a2b837127c08b",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x12ea8f6052767a69d0d9b10c88d614c661bb4385cd919e4241618b9cedb3f54e",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "",
      "token_address": "",
      "contract_wallet": "",
      "hash": "0xa4305f54d61894f03d079220de89df8522a201381f5c249b48361928b376bb56",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x5ddaefbc909f84b9c2b163522f81915566e7d672baafc6cbcc668d573058f2c9",
      "height": "0",
      "amount": "0x16345785d8a0000"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x7d63724f6dfcd499400cbf185fbeda12c5841bdd6b2ee93f2e4c171b6d8ede17",
      "height": "0",
      "amount": "0x38d7ea4c68000"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0xd08173ffae476504586d46d2daf68b1512fbd0e19b2379c059fc8a4a0d6ac8d9",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x72a884280f513e0dab96fafcd8b20bba6661cbe74ba6713c83431e9b0531d452",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x28e06c788af163f6ea196fbe22062ce2a6b525cb573afaa840c59176a7fa9f7a",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x557d64dfb5aecee31a02d2588b8234e6dd93efed882a5effc63c9f3afe891085",
      "height": "0",
      "amount": "0x16345785d8a0000"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0xed86d4b494a6f6e65f5e67b46a719fb0c1d110903097d4dff8ace0194d53e5ef",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x79853159fbc21d6a82ede727487338a3a0ac2369ab1c7179baa7d82b52a9e6bb",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x291cf6aaae333288525c4db27f85cb492f6bb0ae9dd8eafb59166b47f53d89d6",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x632ef71078fd7fde757c44d3c4d76272e6f3d43e32dcdd2d4d3f744bf19393d0",
      "height": "0",
      "amount": "0x38d7ea4c68000"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x4e5fc40a00b6e079bfd84d8b325e4a4db099264fa861b8a1b4cb35ef7e2e74f0",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x8449ea22d19e04e003729111eaa192ee64785a7fc83074249692b8fccd124914",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0xb4d6358aa6111d0b9b7eb204037c81e105669512249fd1deb533e70ef27eb457",
      "height": "0",
      "amount": "0x5af3107a4000"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x03cb57796cae1be8ede3cab9e018e80beb293e3861e12d9be3e0c26a94b4ca97",
      "height": "0",
      "amount": "0x2386f26fc10000"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0xf5ea66a7131cfb8f7b722d64af6b530c7c1e160af9f0335293ef0401ab2d7b80",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x8d3639a4b845ff3c7f886224299e1fcddfd1bd3349356b6d70e6d92aaced800d",
      "height": "0",
      "amount": "0x5af3107a4000"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x7ba553c24a05ff10b86bd7a76f205868d094bafcfdc764578fb9c7d44be017c3",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0xb2714f25f1f0a9f6126a3bd269181aa2900be26552f9399edb0f9bdd5c8df061",
      "height": "0",
      "amount": "0x3635c9adc5dea00000"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x8b0662f0413ca5981c8c0f291edaf8b72423bf7684a97725dd0ec21bb44dc125",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0xe67c30fdfc7616627d9a5fb59968114ce8dd4aa1745279cd9a8bc41bc4f2ca9a",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0xa8c3dd2ab5dbb2c92570fed33fc5e85e01ac058147726b1e4e46cffeb004d7b9",
      "height": "0",
      "amount": "0x11c37937e08000"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x5303fb5c8dd6ba1edbe1c3438dc4ebd6dac8ebbdccee5364a5506d0501be97ef",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x81b51ec93a8c675b91743bc6a4a44cf2b4b151b9de37f473a2c70dc5ac2c520e",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x51d334abfa265ac93acf73b84351456a65bc72e8eac70bd8e960214f9d2d771b",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x649528323b95087bb666248407546c28c135800526b44054f50ce16fe7bc3aa3",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x2f0ec11dd64be8c6dd8f634085dee8cb53d68bc81f5999549951cce99d36b7fd",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x42e7ad4fae15ea3fcd52a2b96b5e6df933d765618157d78082a7d3663074ec44",
      "height": "0",
      "amount": "0xde0b6b3a7640000"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x24853345c550475f27b3dac70b298dfd0ae2a8cbbd51b0866e0e7ece7d132d55",
      "height": "0",
      "amount": "0x0"
    },
    {
      "from": "******************************************",
      "to": "******************************************",
      "token_address": "******************************************",
      "contract_wallet": "******************************************",
      "hash": "0x6c50fd60f939f1ce4d707207bcdb6dad647fcbf42594d154f25fd0ee60411410",
      "height": "0",
      "amount": "0x0"
    }
  ]
}
```

## get account 

- request
```
grpcurl -plaintext -d '{
  "chain": "Polygon",
  "network": "mainnet",
  "address": "******************************************"
}' 127.0.0.1:8189 dapplink.account.WalletAccountService.getAccount


```
- response
```
{
  "code": "SUCCESS",
  "msg": "get account response success",
  "network": "",
  "account_number": "0",
  "sequence": "32",
  "balance": "0"
}
```

## get tx by address
- request
```
grpcurl -plaintext -d '{
  "chain": "Polygon",
  "network": "mainnet",
  "address": "******************************************"

}' 127.0.0.1:8189 dapplink.account.WalletAccountService.getTxByAddress
```

- response
```

```

