// Package chain 定义了区块链适配器的通用接口
// 该包为不同区块链提供统一的操作接口，支持多种区块链的账户管理、交易处理等功能
package chain

import "github.com/dapplink-labs/wallet-chain-account/rpc/account"

// IChainAdaptor 定义了区块链适配器的通用接口
// 所有支持的区块链都需要实现这个接口，提供统一的操作方法
// 包括地址管理、区块查询、交易处理、NFT操作等功能
type IChainAdaptor interface {
	// GetSupportChains 获取支持的区块链信息
	GetSupportChains(req *account.SupportChainsRequest) (*account.SupportChainsResponse, error)

	// ConvertAddress 将公钥转换为对应区块链的地址格式
	ConvertAddress(req *account.ConvertAddressRequest) (*account.ConvertAddressResponse, error)

	// ValidAddress 验证地址格式是否正确
	ValidAddress(req *account.ValidAddressRequest) (*account.ValidAddressResponse, error)

	// GetBlockByNumber 根据区块号获取区块信息
	GetBlockByNumber(req *account.BlockNumberRequest) (*account.BlockResponse, error)

	// GetBlockByHash 根据区块哈希获取区块信息
	GetBlockByHash(req *account.BlockHashRequest) (*account.BlockResponse, error)

	// GetBlockHeaderByHash 根据区块哈希获取区块头信息
	GetBlockHeaderByHash(req *account.BlockHeaderHashRequest) (*account.BlockHeaderResponse, error)

	// GetBlockHeaderByNumber 根据区块号获取区块头信息
	GetBlockHeaderByNumber(req *account.BlockHeaderNumberRequest) (*account.BlockHeaderResponse, error)

	// GetAccount 获取账户信息（余额、nonce等）
	GetAccount(req *account.AccountRequest) (*account.AccountResponse, error)

	// GetFee 获取交易手续费估算
	GetFee(req *account.FeeRequest) (*account.FeeResponse, error)

	// SendTx 发送已签名的交易到区块链网络
	SendTx(req *account.SendTxRequest) (*account.SendTxResponse, error)

	// GetTxByAddress 根据地址获取相关的交易列表
	GetTxByAddress(req *account.TxAddressRequest) (*account.TxAddressResponse, error)

	// GetTxByHash 根据交易哈希获取交易详情
	GetTxByHash(req *account.TxHashRequest) (*account.TxHashResponse, error)

	// GetBlockByRange 获取指定范围内的区块信息
	GetBlockByRange(req *account.BlockByRangeRequest) (*account.BlockByRangeResponse, error)

	// BuildUnSignTransaction 构建未签名的交易
	BuildUnSignTransaction(req *account.UnSignTransactionRequest) (*account.UnSignTransactionResponse, error)

	// BuildSignedTransaction 构建已签名的交易
	BuildSignedTransaction(req *account.SignedTransactionRequest) (*account.SignedTransactionResponse, error)

	// DecodeTransaction 解码交易数据
	DecodeTransaction(req *account.DecodeTransactionRequest) (*account.DecodeTransactionResponse, error)

	// VerifySignedTransaction 验证已签名交易的有效性
	VerifySignedTransaction(req *account.VerifyTransactionRequest) (*account.VerifyTransactionResponse, error)

	// GetExtraData 获取区块链特定的额外数据
	GetExtraData(req *account.ExtraDataRequest) (*account.ExtraDataResponse, error)

	// GetNftListByAddress 根据地址获取NFT列表
	GetNftListByAddress(req *account.NftAddressRequest) (*account.NftAddressResponse, error)
}
